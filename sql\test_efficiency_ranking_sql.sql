-- 测试消耗效率排行榜查询SQL
-- 用于验证修复后的SQL语句是否正确

-- 1. 测试基本的效率排行榜查询（修复后的版本）
SELECT
    i.item_id,
    i.item_name,
    i.item_code,
    i.item_type,
    CASE i.item_type
        WHEN 1 THEN '消耗品'
        WHEN 2 THEN '备品备件'
        ELSE '未知'
    END as item_type_name,
    i.spec_model,
    i.unit,
    COALESCE(e.efficiency_score, 0) as efficiency_score,
    COALESCE(e.efficiency_level, 'unknown') as efficiency_level,
    CASE COALESCE(e.efficiency_level, 'unknown')
        WHEN 'excellent' THEN '优秀'
        WHEN 'good' THEN '良好'
        WHEN 'average' THEN '一般'
        WHEN 'poor' THEN '较差'
        ELSE '未知'
    END as efficiency_level_name,
    COALESCE(e.consumption_per_unit, 0) as consumption_per_unit,
    COALESCE(e.savings_amount, 0) as savings_amount,
    COALESCE(SUM(cs.total_quantity), 0) as total_consumption,
    COALESCE(SUM(cs.total_amount), 0) as total_amount
FROM item_base_info i
LEFT JOIN item_consumption_efficiency e ON i.item_id = e.item_id
LEFT JOIN item_consumption_summary cs ON i.item_id = cs.item_id
WHERE i.deleted = 0
    AND e.efficiency_score IS NOT NULL
GROUP BY i.item_id, e.efficiency_id
ORDER BY e.efficiency_score DESC
LIMIT 5;

-- 2. 测试带时间条件的效率排行榜查询
SELECT
    i.item_id,
    i.item_name,
    i.item_code,
    i.item_type,
    CASE i.item_type
        WHEN 1 THEN '消耗品'
        WHEN 2 THEN '备品备件'
        ELSE '未知'
    END as item_type_name,
    i.spec_model,
    i.unit,
    COALESCE(e.efficiency_score, 0) as efficiency_score,
    COALESCE(e.efficiency_level, 'unknown') as efficiency_level,
    CASE COALESCE(e.efficiency_level, 'unknown')
        WHEN 'excellent' THEN '优秀'
        WHEN 'good' THEN '良好'
        WHEN 'average' THEN '一般'
        WHEN 'poor' THEN '较差'
        ELSE '未知'
    END as efficiency_level_name,
    COALESCE(e.consumption_per_unit, 0) as consumption_per_unit,
    COALESCE(e.savings_amount, 0) as savings_amount,
    COALESCE(SUM(cs.total_quantity), 0) as total_consumption,
    COALESCE(SUM(cs.total_amount), 0) as total_amount
FROM item_base_info i
LEFT JOIN item_consumption_efficiency e ON i.item_id = e.item_id
LEFT JOIN item_consumption_summary cs ON i.item_id = cs.item_id
WHERE i.deleted = 0
    AND e.efficiency_score IS NOT NULL
    AND e.period_start_date >= '2024-01-01'
    AND e.period_end_date <= '2024-12-31'
    AND cs.consumption_date BETWEEN '2024-01-01' AND '2024-12-31'
GROUP BY i.item_id, e.efficiency_id
ORDER BY e.efficiency_score DESC
LIMIT 5;

-- 3. 测试带分析周期条件的效率排行榜查询
SELECT
    i.item_id,
    i.item_name,
    i.item_code,
    i.item_type,
    CASE i.item_type
        WHEN 1 THEN '消耗品'
        WHEN 2 THEN '备品备件'
        ELSE '未知'
    END as item_type_name,
    i.spec_model,
    i.unit,
    COALESCE(e.efficiency_score, 0) as efficiency_score,
    COALESCE(e.efficiency_level, 'unknown') as efficiency_level,
    CASE COALESCE(e.efficiency_level, 'unknown')
        WHEN 'excellent' THEN '优秀'
        WHEN 'good' THEN '良好'
        WHEN 'average' THEN '一般'
        WHEN 'poor' THEN '较差'
        ELSE '未知'
    END as efficiency_level_name,
    COALESCE(e.consumption_per_unit, 0) as consumption_per_unit,
    COALESCE(e.savings_amount, 0) as savings_amount,
    COALESCE(SUM(cs.total_quantity), 0) as total_consumption,
    COALESCE(SUM(cs.total_amount), 0) as total_amount
FROM item_base_info i
LEFT JOIN item_consumption_efficiency e ON i.item_id = e.item_id
LEFT JOIN item_consumption_summary cs ON i.item_id = cs.item_id
WHERE i.deleted = 0
    AND e.efficiency_score IS NOT NULL
    AND e.period_start_date >= '2024-01-01'
    AND e.period_end_date <= '2024-12-31'
    AND cs.consumption_date BETWEEN '2024-01-01' AND '2024-12-31'
    AND e.analysis_period = 'monthly'
GROUP BY i.item_id, e.efficiency_id
ORDER BY e.efficiency_score DESC
LIMIT 5;

-- 4. 检查相关表是否存在数据
SELECT 'item_base_info' as table_name, COUNT(*) as record_count FROM item_base_info WHERE deleted = 0
UNION ALL
SELECT 'item_consumption_efficiency' as table_name, COUNT(*) as record_count FROM item_consumption_efficiency
UNION ALL
SELECT 'item_consumption_summary' as table_name, COUNT(*) as record_count FROM item_consumption_summary;
