# 维护报表功能实现文档

## 项目概述

### 功能目标
基于现有维护模块，实现包含维护任务完成率、故障响应时间、维护成本等的统计报表功能，支持图表展示。复用资产盘点模块已有的图表展示技术方案，避免重复集成。

### 技术架构
- **后端框架**: RuoYi框架 (Spring Boot + MyBatis Plus)
- **前端框架**: Vue.js + Element UI
- **图表组件**: 复用资产盘点模块的Chart.js图表方案
- **数据库**: MySQL 8.0+

## 功能模块设计

### 1. 维护报表统计模块

#### 1.1 维护任务完成率统计
**统计维度**:
- 按时间维度：日、周、月、季度、年度
- 按部门维度：各部门维护任务完成情况
- 按资产类型维度：不同资产类型的维护完成率
- 按优先级维度：不同优先级任务的完成情况

**统计指标**:
- 总任务数量
- 已完成任务数量
- 完成率百分比
- 平均完成时间
- 逾期任务数量
- 逾期率

#### 1.2 故障响应时间统计
**统计维度**:
- 按故障类型：电气故障、机械故障、控制系统故障等
- 按紧急程度：一般、较急、紧急、特急
- 按处理人员：不同处理人员的响应效率
- 按时间段：工作日vs非工作日响应时间对比

**统计指标**:
- 平均响应时间（从申报到受理）
- 平均处理时间（从受理到完成）
- 故障解决率
- 超时处理数量
- 重复故障率

#### 1.3 维护成本统计
**成本构成**:
- 人工成本：维护人员工时费用
- 材料成本：备品备件消耗费用
- 外包成本：外部维护服务费用
- 停机成本：设备停机造成的损失

**统计维度**:
- 按资产维度：单个资产的维护成本趋势
- 按部门维度：各部门维护成本分析
- 按时间维度：维护成本的时间趋势
- 按维护类型：预防性维护vs故障维护成本对比

## 数据库设计

### 2.1 维护成本记录表
```sql
CREATE TABLE `maintenance_cost_record` (
  `cost_id` varchar(32) NOT NULL COMMENT '成本记录ID',
  `task_id` varchar(32) DEFAULT NULL COMMENT '维护任务ID',
  `fault_id` varchar(32) DEFAULT NULL COMMENT '故障申报ID',
  `cost_type` tinyint(4) NOT NULL COMMENT '成本类型(1-人工成本,2-材料成本,3-外包成本,4-停机成本)',
  `cost_amount` decimal(10,2) NOT NULL COMMENT '成本金额',
  `cost_description` text COMMENT '成本说明',
  `record_time` datetime NOT NULL COMMENT '记录时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除(0-否,1-是)',
  PRIMARY KEY (`cost_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_fault_id` (`fault_id`),
  KEY `idx_record_time` (`record_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='维护成本记录表';
```

### 2.2 维护报表配置表
```sql
CREATE TABLE `maintenance_report_config` (
  `config_id` varchar(32) NOT NULL COMMENT '配置ID',
  `report_name` varchar(100) NOT NULL COMMENT '报表名称',
  `report_type` tinyint(4) NOT NULL COMMENT '报表类型(1-完成率,2-响应时间,3-成本分析)',
  `time_dimension` tinyint(4) NOT NULL COMMENT '时间维度(1-日,2-周,3-月,4-季,5-年)',
  `filter_config` json COMMENT '筛选条件配置',
  `chart_config` json COMMENT '图表配置',
  `is_default` tinyint(4) DEFAULT '0' COMMENT '是否默认配置',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='维护报表配置表';
```

## 后端实现

### 3.1 实体类设计

#### MaintenanceCostRecord.java
```java
@TableName(value = "maintenance_cost_record")
@Data
public class MaintenanceCostRecord implements Serializable {
    @TableId(type = IdType.INPUT)
    private String costId;
    
    private String taskId;
    private String faultId;
    private Integer costType;
    private BigDecimal costAmount;
    private String costDescription;
    private Date recordTime;
    private Date createTime;
    private String createBy;
    private Date updateTime;
    private String updateBy;
    private Integer deleted;
}
```

#### MaintenanceReportVo.java
```java
@Data
public class MaintenanceReportVo {
    
    // 维护任务完成率统计
    @Data
    public static class TaskCompletionVo {
        private Integer totalTasks;
        private Integer completedTasks;
        private BigDecimal completionRate;
        private Integer overdueTasks;
        private BigDecimal overdueRate;
        private BigDecimal avgCompletionDays;
    }
    
    // 故障响应时间统计
    @Data
    public static class FaultResponseVo {
        private BigDecimal avgResponseHours;
        private BigDecimal avgProcessingHours;
        private Integer totalFaults;
        private Integer resolvedFaults;
        private BigDecimal resolutionRate;
        private Integer overtimeFaults;
    }
    
    // 维护成本统计
    @Data
    public static class MaintenanceCostVo {
        private BigDecimal totalCost;
        private BigDecimal laborCost;
        private BigDecimal materialCost;
        private BigDecimal outsourceCost;
        private BigDecimal downtimeCost;
        private List<CostTrendVo> costTrends;
    }
    
    @Data
    public static class CostTrendVo {
        private String period;
        private BigDecimal amount;
        private String costType;
    }
}
```

### 3.2 服务层实现

#### MaintenanceReportService.java
```java
public interface MaintenanceReportService {
    
    /**
     * 获取维护任务完成率统计
     */
    MaintenanceReportVo.TaskCompletionVo getTaskCompletionStatistics(
        Date startDate, Date endDate, String deptId, String assetType);
    
    /**
     * 获取故障响应时间统计
     */
    MaintenanceReportVo.FaultResponseVo getFaultResponseStatistics(
        Date startDate, Date endDate, Integer faultType, Integer urgencyLevel);
    
    /**
     * 获取维护成本统计
     */
    MaintenanceReportVo.MaintenanceCostVo getMaintenanceCostStatistics(
        Date startDate, Date endDate, String deptId, String assetId);
    
    /**
     * 生成维护报表图表数据
     */
    Map<String, Object> generateMaintenanceChartData(
        String reportType, Date startDate, Date endDate, Map<String, Object> filters);
    
    /**
     * 导出维护报表
     */
    void exportMaintenanceReport(HttpServletResponse response, 
        String reportType, Date startDate, Date endDate, Map<String, Object> filters);
}
```

### 3.3 控制器实现

#### MaintenanceReportController.java
```java
@RestController
@RequestMapping("/maintenance/report")
public class MaintenanceReportController extends BaseController {
    
    @Autowired
    private MaintenanceReportService reportService;
    
    /**
     * 获取维护任务完成率统计
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:completion')")
    @GetMapping("/task-completion")
    public AjaxResult getTaskCompletionStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String deptId,
            @RequestParam(required = false) String assetType) {
        
        MaintenanceReportVo.TaskCompletionVo statistics = 
            reportService.getTaskCompletionStatistics(startDate, endDate, deptId, assetType);
        return success(statistics);
    }
    
    /**
     * 获取故障响应时间统计
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:response')")
    @GetMapping("/fault-response")
    public AjaxResult getFaultResponseStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) Integer faultType,
            @RequestParam(required = false) Integer urgencyLevel) {
        
        MaintenanceReportVo.FaultResponseVo statistics = 
            reportService.getFaultResponseStatistics(startDate, endDate, faultType, urgencyLevel);
        return success(statistics);
    }
    
    /**
     * 获取维护成本统计
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:cost')")
    @GetMapping("/maintenance-cost")
    public AjaxResult getMaintenanceCostStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String deptId,
            @RequestParam(required = false) String assetId) {
        
        MaintenanceReportVo.MaintenanceCostVo statistics = 
            reportService.getMaintenanceCostStatistics(startDate, endDate, deptId, assetId);
        return success(statistics);
    }
    
    /**
     * 生成维护报表图表数据
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:chart')")
    @PostMapping("/chart-data")
    public AjaxResult generateChartData(@RequestBody MaintenanceReportRequest request) {
        Map<String, Object> chartData = reportService.generateMaintenanceChartData(
            request.getReportType(), request.getStartDate(), request.getEndDate(), request.getFilters());
        return success(chartData);
    }
    
    /**
     * 导出维护报表
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:export')")
    @PostMapping("/export")
    public void exportReport(HttpServletResponse response, @RequestBody MaintenanceReportRequest request) {
        reportService.exportMaintenanceReport(response, 
            request.getReportType(), request.getStartDate(), request.getEndDate(), request.getFilters());
    }
}
```

## 前端实现

### 4.1 页面结构
```
device_monitor-ui/src/views/maintenance/report/
├── index.vue                    # 维护报表主页面
├── components/
│   ├── TaskCompletionChart.vue  # 任务完成率图表组件
│   ├── FaultResponseChart.vue   # 故障响应时间图表组件
│   ├── CostAnalysisChart.vue    # 成本分析图表组件
│   └── ReportFilter.vue         # 报表筛选组件
└── config/
    └── chartConfig.js           # 图表配置文件
```

### 4.2 图表组件复用方案

复用资产盘点模块的图表技术方案，包括：
- Chart.js图表库
- 图表配置管理
- 数据格式化处理
- 响应式图表设计

### 4.3 主要接口

#### 维护任务完成率统计
- `GET /maintenance/report/task-completion` - 获取任务完成率统计
- `POST /maintenance/report/chart-data` - 生成完成率图表数据

#### 故障响应时间统计  
- `GET /maintenance/report/fault-response` - 获取故障响应时间统计
- `POST /maintenance/report/chart-data` - 生成响应时间图表数据

#### 维护成本统计
- `GET /maintenance/report/maintenance-cost` - 获取维护成本统计
- `POST /maintenance/report/chart-data` - 生成成本分析图表数据

#### 报表导出
- `POST /maintenance/report/export` - 导出维护报表Excel文件

## 权限配置

### 5.1 权限定义
```
maintenance:report:completion  # 查看任务完成率报表
maintenance:report:response    # 查看故障响应时间报表  
maintenance:report:cost        # 查看维护成本报表
maintenance:report:chart       # 查看报表图表
maintenance:report:export      # 导出报表
```

### 5.2 菜单配置
在系统菜单中添加"维护报表"菜单项，包含：
- 任务完成率分析
- 故障响应时间分析
- 维护成本分析
- 综合报表

## 开发计划

### 第一阶段：数据库和后端开发（3个工作日）
1. 创建维护成本记录表和报表配置表
2. 实现维护报表服务层
3. 实现维护报表控制器
4. 编写单元测试

### 第二阶段：前端开发（4个工作日）
1. 创建报表页面和组件
2. 复用资产盘点模块的图表方案
3. 实现报表筛选和数据展示
4. 集成Excel导出功能

### 第三阶段：测试和优化（2个工作日）
1. 功能测试和性能优化
2. 用户界面优化
3. 文档完善

## 注意事项

1. **避免重复开发**：充分复用资产盘点模块的图表展示技术方案
2. **数据一致性**：确保统计数据与维护任务、故障申报数据的一致性
3. **性能优化**：对于大数据量的统计查询，考虑使用缓存和分页
4. **权限控制**：严格按照RuoYi框架的权限体系进行访问控制
5. **扩展性**：预留接口扩展空间，便于后续添加新的统计维度

这份文档提供了维护报表功能的完整实现方案，您觉得这个设计是否符合您的需求？是否需要我对某些部分进行详细说明或调整？
