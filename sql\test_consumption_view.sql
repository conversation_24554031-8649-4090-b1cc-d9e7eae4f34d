-- 测试消耗明细视图的SQL
-- 用于验证视图创建是否正确

-- 1. 测试出库数据查询
SELECT 
    CONCAT('OUT_', od.detail_id) as consumption_id,
    od.item_id,
    od.warehouse_id,
    DATE(o.business_date) as consumption_date,
    CASE o.outbound_type
        WHEN 1 THEN 1  -- 生产领用 -> 生产消耗
        WHEN 2 THEN 3  -- 部门领用 -> 领用消耗
        WHEN 3 THEN 2  -- 维护领用 -> 维护消耗
        ELSE 4         -- 其他 -> 其他消耗
    END as consumption_type,
    od.quantity as consumption_quantity,
    od.amount as consumption_amount,
    od.unit_price,
    o.outbound_id as source_id,
    'outbound' as source_type,
    o.recipient_name,
    o.recipient_dept,
    od.remark,
    o.create_time
FROM item_outbound_detail od
INNER JOIN item_outbound o ON od.outbound_id = o.outbound_id
WHERE o.status = 4  -- 只统计已审核通过的出库单
  AND (o.del_flag = '0' OR o.del_flag IS NULL)  -- 排除已删除的记录
LIMIT 5;

-- 2. 测试领用数据查询
SELECT 
    CONCAT('REQ_', rd.detail_id) as consumption_id,
    rd.item_id,
    rd.warehouse_id,
    DATE(r.create_time) as consumption_date,
    3 as consumption_type,  -- 领用消耗
    rd.actual_quantity as consumption_quantity,
    (rd.actual_quantity * COALESCE(i.unit_price, 0)) as consumption_amount,
    COALESCE(i.unit_price, 0) as unit_price,
    r.requisition_id as source_id,
    'requisition' as source_type,
    u.nick_name as recipient_name,
    d.dept_name as recipient_dept,
    rd.remark,
    r.create_time
FROM item_requisition_detail rd
INNER JOIN item_requisition r ON rd.requisition_id = r.requisition_id
LEFT JOIN sys_user u ON r.applicant_id = u.user_id
LEFT JOIN sys_dept d ON r.dept_id = d.dept_id
LEFT JOIN (
    SELECT item_id, AVG(unit_price) as unit_price
    FROM item_outbound_detail
    WHERE unit_price > 0
    GROUP BY item_id
) i ON rd.item_id = i.item_id
WHERE r.status = 6  -- 只统计已完成的领用单
  AND (r.del_flag = '0' OR r.del_flag IS NULL)  -- 排除已删除的记录
LIMIT 5;

-- 3. 检查相关表是否存在
SHOW TABLES LIKE 'item_%';
SHOW TABLES LIKE 'sys_%';

-- 4. 检查表结构
DESCRIBE item_outbound;
DESCRIBE item_outbound_detail;
DESCRIBE item_requisition;
DESCRIBE item_requisition_detail;
DESCRIBE sys_user;
DESCRIBE sys_dept;
