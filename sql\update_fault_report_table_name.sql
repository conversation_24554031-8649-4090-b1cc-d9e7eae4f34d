-- 数据库表名更新脚本
-- 将 fault_report 表重命名为 asset_fault_report

-- 如果已经存在 fault_report 表，则重命名
-- 注意：执行前请确保备份数据
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'fault_report');

-- 如果 fault_report 表存在，则重命名为 asset_fault_report
SET @sql = IF(@table_exists > 0, 
              'RENAME TABLE fault_report TO asset_fault_report', 
              'SELECT "Table fault_report does not exist" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证表名更改结果
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'asset_fault_report') 
        THEN 'SUCCESS: Table renamed to asset_fault_report'
        ELSE 'WARNING: asset_fault_report table not found'
    END as result;

-- 显示当前数据库中与故障申报相关的表
SELECT table_name, table_comment 
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND (table_name LIKE '%fault%' OR table_name LIKE '%asset_fault%')
ORDER BY table_name;
