# 资产故障申报功能接口文档

## 概述

资产故障申报功能是维护模块的重要组成部分，主要用于用户快速申报设备故障，系统自动生成维护任务并分配给相应的处理人员。该功能特别适合在微信小程序端使用，提供便捷的移动端故障申报体验。

**数据库表名**: `asset_fault_report`

## 功能特点

1. **快速申报**：用户可以通过简单的表单快速提交故障申报
2. **自动任务生成**：系统根据故障申报自动创建维护任务
3. **智能分级**：根据紧急程度自动设置任务优先级
4. **实时跟踪**：用户可以实时查看故障处理进度
5. **图片支持**：支持上传故障现场照片
6. **扫码申报**：支持扫描资产二维码快速选择故障设备

## 接口列表

### 1. 故障申报相关接口

#### 1.1 提交故障申报

- **接口URL**: `POST /maintenance/fault/report`
- **功能描述**: 用户提交故障申报，系统自动生成维护任务
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| assetId | String | 是 | 资产ID |
| assetName | String | 否 | 资产名称（用于显示） |
| faultTitle | String | 是 | 故障标题 |
| faultDescription | String | 是 | 故障描述 |
| faultType | Integer | 是 | 故障类型(1-5) |
| urgencyLevel | Integer | 是 | 紧急程度(1-4) |
| reportLocation | String | 否 | 申报位置 |
| contactPhone | String | 否 | 联系电话 |
| images | Array | 否 | 故障图片列表 |
| remark | String | 否 | 备注说明 |

**请求示例**:

```json
{
  "assetId": "**********",
  "assetName": "中央空调主机",
  "faultTitle": "空调制冷效果差",
  "faultDescription": "空调运行正常但制冷效果明显下降，室内温度无法降到设定温度",
  "faultType": 2,
  "urgencyLevel": 3,
  "reportLocation": "设备楼3层机房",
  "contactPhone": "13800138000",
  "images": ["image1.jpg", "image2.jpg"],
  "remark": "需要尽快处理"
}
```

- **返回结果**:

```json
{
  "code": 200,
  "msg": "故障申报成功",
  "data": {
    "faultId": "FT20250401001",
    "taskId": "MT20250401003"
  }
}
```

#### 1.2 查询我的故障申报

- **接口URL**: `GET /maintenance/fault/my-reports`
- **功能描述**: 查询当前用户提交的故障申报记录
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| status | Integer | 否 | 故障状态筛选 |
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 页大小，默认10 |

- **返回结果**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 3,
    "size": 10,
    "current": 1,
    "pages": 1,
    "records": [
      {
        "faultId": "5325b269b5fc4f8aa613761a7e33f946",
        "faultTitle": "测试故障申报",
        "faultDescription": "这是一个测试故障申报，用于验证系统功能",
        "assetId": "**********",
        "assetName": null,
        "assetCode": null,
        "assetLocation": null,
        "faultType": 2,
        "faultTypeName": "机械故障",
        "urgencyLevel": 3,
        "urgencyLevelName": "紧急",
        "status": 1,
        "statusName": "已提交",
        "reportLocation": "测试位置",
        "contactPhone": "13800138000",
        "reportTime": "2025-07-16T09:47:20.000+08:00",
        "reporterId": 1,
        "reporterName": "超级管理员",
        "handlerId": null,
        "handlerName": null,
        "acceptTime": null,
        "estimatedCompleteTime": null,
        "actualCompleteTime": null,
        "handleDescription": null,
        "images": [],
        "taskId": "ed670d5dc7844d378d1a7c307bf1bdac",
        "remark": "测试备注",
        "createTime": "2025-07-16T09:47:20.000+08:00",
        "updateTime": null,
        "createBy": "admin",
        "updateBy": null
      }
    ]
  }
}
```

#### 1.3 查询故障申报详情

- **接口URL**: `GET /maintenance/fault/{faultId}`
- **功能描述**: 查询指定故障申报的详细信息
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| faultId | String | 是 | 故障申报ID |

- **返回结果**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "faultId": "FT20250401001",
    "faultTitle": "空调制冷效果差",
    "faultDescription": "空调运行正常但制冷效果明显下降，室内温度无法降到设定温度",
    "assetId": "**********",
    "assetName": "中央空调主机",
    "assetCode": "KT-2023-001",
    "assetLocation": "设备楼3层机房",
    "faultType": 2,
    "faultTypeName": "机械故障",
    "urgencyLevel": 3,
    "urgencyLevelName": "紧急",
    "status": 3,
    "statusName": "处理中",
    "reportLocation": "设备楼3层机房",
    "contactPhone": "13800138000",
    "reportTime": "2025-04-01 09:30:00",
    "reporterName": "张三",
    "handlerId": 105,
    "handlerName": "李工",
    "acceptTime": "2025-04-01 10:00:00",
    "estimatedCompleteTime": "2025-04-01 16:00:00",
    "actualCompleteTime": null,
    "handleDescription": "已检查制冷系统，发现冷凝器结垢严重，正在清洗处理",
    "images": ["image1.jpg", "image2.jpg"],
    "taskId": "MT20250401003"
  }
}
```

### 2. 辅助接口

#### 2.1 获取故障类型选项

- **接口URL**: `GET /maintenance/fault/types`
- **功能描述**: 获取故障类型选项列表
- **返回结果**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {"value": 1, "label": "电气故障"},
    {"value": 2, "label": "机械故障"},
    {"value": 3, "label": "控制系统故障"},
    {"value": 4, "label": "安全故障"},
    {"value": 5, "label": "其他故障"}
  ]
}
```

#### 2.2 获取紧急程度选项

- **接口URL**: `GET /maintenance/fault/urgency-levels`
- **功能描述**: 获取紧急程度选项列表
- **返回结果**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {"value": 1, "label": "一般", "description": "不影响正常使用"},
    {"value": 2, "label": "较急", "description": "影响部分功能"},
    {"value": 3, "label": "紧急", "description": "严重影响使用"},
    {"value": 4, "label": "特急", "description": "存在安全隐患"}
  ]
}
```

#### 2.3 获取故障申报统计信息

- **接口URL**: `GET /maintenance/fault/statistics`
- **权限**: `maintenance:fault:list`
- **功能描述**: 获取当前用户的故障申报统计信息
- **返回结果**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "submitted": 2,
    "accepted": 0,
    "processing": 0,
    "completed": 0,
    "closed": 0
  }
}
```

#### 2.4 根据资产ID查询故障申报

- **接口URL**: `GET /maintenance/fault/asset/{assetId}`
- **功能描述**: 查询指定资产的故障申报历史
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| assetId | String | 是 | 资产ID |

- **返回结果**: 返回List格式的故障申报记录，字段同查询我的故障申报接口中的records数组

### 3. 管理接口（PC端使用）

#### 3.1 分页查询故障申报列表

- **接口URL**: `GET /maintenance/fault/list`
- **权限**: `maintenance:fault:list`
- **功能描述**: 管理员查询所有故障申报记录
- **请求参数**: 支持复杂查询条件，详见FaultReportSearchRequest类

#### 3.2 受理故障申报

- **接口URL**: `POST /maintenance/fault/accept`
- **权限**: `maintenance:fault:accept`
- **功能描述**: 处理人员受理故障申报
- **请求参数**:

```json
{
  "faultId": "故障申报ID",
  "handlerId": 处理人员ID,
  "handleDescription": "受理说明"
}
```

#### 3.3 更新故障处理进度

- **接口URL**: `POST /maintenance/fault/progress`
- **权限**: `maintenance:fault:handle`
- **功能描述**: 更新故障处理进度
- **请求参数**:

```json
{
  "faultId": "故障申报ID",
  "handleDescription": "处理进度描述"
}
```

#### 3.4 完成故障处理

- **接口URL**: `POST /maintenance/fault/complete`
- **权限**: `maintenance:fault:handle`
- **功能描述**: 标记故障处理完成
- **请求参数**:

```json
{
  "faultId": "故障申报ID",
  "handleDescription": "完成说明"
}
```

#### 3.5 关闭故障申报

- **接口URL**: `POST /maintenance/fault/close`
- **权限**: `maintenance:fault:close`
- **功能描述**: 关闭故障申报
- **请求参数**:

```json
{
  "faultId": "故障申报ID",
  "closeReason": "关闭原因"
}
```

#### 3.6 查询紧急故障申报

- **接口URL**: `GET /maintenance/fault/urgent`
- **权限**: `maintenance:fault:list`
- **功能描述**: 查询紧急程度为"紧急"及以上的未处理故障申报

#### 3.7 查询未处理故障申报

- **接口URL**: `GET /maintenance/fault/unhandled`
- **权限**: `maintenance:fault:list`
- **功能描述**: 查询状态为"已提交"的故障申报

#### 3.8 查询我处理的故障申报

- **接口URL**: `GET /maintenance/fault/my-handling`
- **权限**: `maintenance:fault:handle`
- **功能描述**: 查询当前用户作为处理人的故障申报

## 数据字典

### 故障类型(faultType)

| 类型码 | 类型名称 |
|-------|---------|
| 1 | 电气故障 |
| 2 | 机械故障 |
| 3 | 控制系统故障 |
| 4 | 安全故障 |
| 5 | 其他故障 |

### 紧急程度(urgencyLevel)

| 紧急程度码 | 紧急程度名称 | 描述 |
|----------|------------|------|
| 1 | 一般 | 不影响正常使用 |
| 2 | 较急 | 影响部分功能 |
| 3 | 紧急 | 严重影响使用 |
| 4 | 特急 | 存在安全隐患 |

### 故障状态(status)

| 状态码 | 状态名称 | 描述 |
|-------|---------|------|
| 1 | 已提交 | 用户已提交故障申报 |
| 2 | 已受理 | 处理人员已受理 |
| 3 | 处理中 | 正在处理故障 |
| 4 | 已完成 | 故障处理完成 |
| 5 | 已关闭 | 故障申报已关闭 |

## 业务流程

### 故障申报流程

1. **用户申报**：用户在小程序中填写故障信息并提交
2. **自动任务生成**：系统根据故障信息自动创建维护任务
3. **任务分配**：系统根据故障类型和紧急程度自动分配处理人员
4. **受理处理**：处理人员受理故障并开始处理
5. **进度更新**：处理人员更新处理进度
6. **完成确认**：处理完成后标记故障为已完成状态

### 状态流转

```
已提交 → 已受理 → 处理中 → 已完成
   ↓         ↓        ↓
 已关闭 ← 已关闭 ← 已关闭
```

## 注意事项

1. **权限控制**：故障申报提交不需要特殊权限，任何用户都可以申报
2. **自动任务**：提交故障申报后会自动生成维护任务
3. **图片处理**：图片需要先上传到文件服务器，然后传递文件路径
4. **实时通知**：建议集成消息推送功能，及时通知相关人员
5. **数据统计**：可以基于故障申报数据进行设备健康度分析

## 部署说明

1. **数据库**：执行 `sql/fault_report.sql` 创建相关表和权限（表名：asset_fault_report）
2. **依赖**：确保维护任务模块正常运行
3. **权限**：为相关角色分配故障申报相关权限
4. **测试**：使用提供的测试数据验证功能
