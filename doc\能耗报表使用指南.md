# 车间生产能耗报表使用指南

## 🚀 快速开始

### 访问方式

1. **启动前端服务**
   ```bash
   cd device_monitor-ui
   npm run dev
   ```

2. **访问地址**
   - 首页：http://localhost:80
   - 能耗管理：http://localhost:80/#/energy
   - 能耗报表：http://localhost:80/#/energy/consumption-report

### 登录系统
- 使用RuoYi系统的默认管理员账号登录
- 或根据实际配置的用户账号登录

## 📊 功能使用

### 1. 能耗管理首页
- 访问路径：`能耗管理 > 能耗管理首页`
- 功能概览：
  - 系统介绍和功能导航
  - 快速统计数据展示
  - 一键跳转到能耗报表

### 2. 车间生产能耗报表
- 访问路径：`能耗管理 > 车间生产能耗报表`
- 主要功能：

#### 数据筛选
1. **车间选择**：从下拉列表选择要查看的车间
2. **时间范围**：选择查询的日期范围
3. **能耗类型**：可选择特定的能耗类型进行筛选
4. 点击"搜索"按钮执行查询

#### 统计卡片
- **总能耗**：显示选定条件下的总能耗量
- **平均单耗**：单位产量的平均能耗
- **能效等级**：当前能效水平评级
- **节能率**：节能效果百分比

#### 图表分析
1. **能耗趋势分析**
   - 支持日、周、月三种时间维度切换
   - 多条线显示不同能耗类型的趋势
   - 可点击图例显示/隐藏特定数据系列

2. **能耗类型分布**
   - 饼图显示各类型能耗占比
   - 悬停查看具体数值和百分比

3. **车间能耗对比**
   - 支持三种对比模式切换：
     - 总能耗：各车间总能耗对比
     - 单位产量能耗：效率对比
     - 能效比：能效水平对比

#### 详细数据表格
- 分页显示详细的能耗记录
- 包含日期、车间、各类能耗、产量等信息
- 支持表格数据导出

### 3. 数据导出
- 点击"导出"按钮
- 系统会提示导出成功（演示功能）
- 实际项目中可导出Excel格式文件

## 🎯 演示数据说明

### 车间列表
- 冲压车间
- 焊装车间
- 涂装车间
- 总装车间
- 发动机车间

### 能耗类型
- **电力**：单位kWh，范围20,000-70,000
- **燃气**：单位m³，范围2,000-7,000
- **蒸汽**：单位t，范围50-150
- **水**：单位t，范围100-300

### 生产数据
- **产量**：单位件，范围5,000-15,000
- **单位产量能耗**：kWh/件
- **能效等级**：A、B、C三个等级

## 💡 操作技巧

### 图表交互
1. **趋势图**
   - 鼠标悬停查看具体数值
   - 点击图例控制数据系列显示
   - 使用工具栏缩放和重置视图

2. **分布图**
   - 悬停查看详细信息
   - 点击扇形区域高亮显示

3. **对比图**
   - 切换不同对比模式
   - 悬停查看具体数值

### 数据筛选
1. **时间范围**
   - 默认显示最近7天数据
   - 可自定义选择任意日期范围
   - 支持快速选择预设时间段

2. **车间筛选**
   - 可选择单个车间查看
   - 清空选择查看所有车间

3. **能耗类型**
   - 可筛选特定能耗类型
   - 清空查看所有类型

## 🔧 技术特性

### 响应式设计
- 支持桌面端、平板端、移动端
- 图表自动适应屏幕尺寸
- 表格支持横向滚动

### 性能优化
- 图表懒加载
- 数据分页加载
- 防抖搜索

### 用户体验
- 加载状态提示
- 友好的错误处理
- 直观的操作反馈

## 🎨 界面说明

### 布局结构
```
页面顶部：筛选条件和操作按钮
├── 统计卡片区域（4个关键指标）
├── 图表区域
│   ├── 左侧：能耗趋势图
│   └── 右侧：能耗分布图
├── 车间对比图（全宽）
└── 详细数据表格
```

### 色彩主题
- **主色调**：蓝色系（#409EFF）
- **辅助色**：绿色、橙色、红色
- **背景色**：浅灰色系
- **文字色**：深灰色系

## 📱 移动端适配

### 响应式断点
- **桌面端**：>= 992px
- **平板端**：768px - 991px
- **移动端**：< 768px

### 移动端优化
- 图表自动调整大小
- 表格支持横向滚动
- 按钮和操作区域适配触摸

## 🔍 故障排除

### 常见问题

1. **页面无法访问**
   - 检查前端服务是否启动
   - 确认端口号是否正确
   - 检查路由配置

2. **图表不显示**
   - 确认ECharts库已正确加载
   - 检查浏览器控制台错误信息
   - 验证数据格式是否正确

3. **数据不更新**
   - 检查模拟数据生成逻辑
   - 确认组件生命周期方法执行
   - 验证响应式数据绑定

### 调试方法
1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 检查Network面板的请求状态
4. 使用Vue DevTools调试组件状态

## 🚀 扩展开发

### 接入真实数据
1. 修改API接口地址
2. 调整数据格式处理
3. 添加错误处理逻辑

### 功能扩展
1. 添加更多图表类型
2. 增加数据钻取功能
3. 集成预警功能
4. 支持报表定制

---

**文档版本**：V1.0  
**创建日期**：2025-01-16  
**最后更新**：2025-01-16
