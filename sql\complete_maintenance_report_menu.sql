-- 维护报表功能完整菜单配置脚本
-- 自动查找父菜单ID并配置维护报表菜单

-- 1. 查找设备维护菜单ID
SET @maintenance_parent_id = (
    SELECT menu_id 
    FROM sys_menu 
    WHERE (menu_name = '设备维护' OR path = 'maintenance') 
    AND menu_type = 'M' 
    LIMIT 1
);

-- 2. 如果设备维护菜单不存在，先创建它
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 
    '设备维护',
    0,                            -- 顶级菜单
    4,                            -- 显示顺序
    'maintenance',                -- 路由地址
    '',                           -- 组件路径（目录类型为空）
    '',                           -- 路由参数
    1,                            -- 是否为外链（1是 0否）
    0,                            -- 是否缓存（0缓存 1不缓存）
    'M',                          -- 菜单类型（M目录 C菜单 F按钮）
    '0',                          -- 显示状态（0显示 1隐藏）
    '0',                          -- 菜单状态（0正常 1停用）
    '',                           -- 权限标识（目录可为空）
    'tool',                       -- 菜单图标
    'admin',                      -- 创建者
    NOW(),                        -- 创建时间
    'admin',                      -- 更新者
    NOW(),                        -- 更新时间
    '设备维护管理目录'             -- 备注
WHERE @maintenance_parent_id IS NULL;

-- 3. 重新获取设备维护菜单ID
SET @maintenance_parent_id = (
    SELECT menu_id 
    FROM sys_menu 
    WHERE (menu_name = '设备维护' OR path = 'maintenance') 
    AND menu_type = 'M' 
    LIMIT 1
);

-- 4. 检查维护报表菜单是否已存在
SET @existing_report_menu = (
    SELECT menu_id 
    FROM sys_menu 
    WHERE menu_name = '维护报表' AND parent_id = @maintenance_parent_id
    LIMIT 1
);

-- 5. 如果维护报表菜单不存在，则创建
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT 
    '维护报表',                    -- 菜单名称
    @maintenance_parent_id,       -- 父菜单ID
    6,                            -- 显示顺序
    'report',                     -- 路由地址
    'maintenance/report/index',   -- 组件路径
    '',                           -- 路由参数
    1,                            -- 是否为外链（1是 0否）
    0,                            -- 是否缓存（0缓存 1不缓存）
    'C',                          -- 菜单类型（M目录 C菜单 F按钮）
    '0',                          -- 显示状态（0显示 1隐藏）
    '0',                          -- 菜单状态（0正常 1停用）
    'maintenance:report:view',    -- 权限标识
    's-data',                     -- 菜单图标
    'admin',                      -- 创建者
    NOW(),                        -- 创建时间
    'admin',                      -- 更新者
    NOW(),                        -- 更新时间
    '维护报表菜单'                 -- 备注
WHERE @existing_report_menu IS NULL;

-- 6. 获取维护报表菜单ID
SET @report_menu_id = (
    SELECT menu_id 
    FROM sys_menu 
    WHERE menu_name = '维护报表' AND parent_id = @maintenance_parent_id
    LIMIT 1
);

-- 7. 添加维护报表相关的按钮权限（如果不存在）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT * FROM (
    SELECT 
        '报表查看' as menu_name,
        @report_menu_id as parent_id,
        1 as order_num,
        '' as path,
        '' as component,
        '' as query,
        1 as is_frame,
        0 as is_cache,
        'F' as menu_type,
        '0' as visible,
        '0' as status,
        'maintenance:report:view' as perms,
        '#' as icon,
        'admin' as create_by,
        NOW() as create_time,
        'admin' as update_by,
        NOW() as update_time,
        '维护报表查看权限' as remark
    UNION ALL
    SELECT 
        '报表导出',
        @report_menu_id,
        2,
        '',
        '',
        '',
        1,
        0,
        'F',
        '0',
        '0',
        'maintenance:report:export',
        '#',
        'admin',
        NOW(),
        'admin',
        NOW(),
        '维护报表导出权限'
    UNION ALL
    SELECT 
        '数据统计',
        @report_menu_id,
        3,
        '',
        '',
        '',
        1,
        0,
        'F',
        '0',
        '0',
        'maintenance:report:stats',
        '#',
        'admin',
        NOW(),
        'admin',
        NOW(),
        '维护报表统计权限'
) AS new_menus
WHERE NOT EXISTS (
    SELECT 1 FROM sys_menu 
    WHERE parent_id = @report_menu_id AND perms = new_menus.perms
);

-- 8. 为管理员角色分配维护报表权限
-- 查找管理员角色ID
SET @admin_role_id = (
    SELECT role_id 
    FROM sys_role 
    WHERE role_key = 'admin' OR role_name = '超级管理员'
    LIMIT 1
);

-- 分配权限给管理员角色
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT @admin_role_id, menu_id 
FROM sys_menu 
WHERE (menu_id = @report_menu_id OR parent_id = @report_menu_id)
AND @admin_role_id IS NOT NULL;

-- 9. 验证配置结果
SELECT 
    '=== 菜单配置验证 ===' as info,
    NULL as menu_id,
    NULL as menu_name,
    NULL as parent_id,
    NULL as path,
    NULL as component,
    NULL as perms,
    NULL as menu_type
UNION ALL
SELECT 
    '设备维护菜单:',
    menu_id,
    menu_name,
    parent_id,
    path,
    component,
    perms,
    menu_type
FROM sys_menu 
WHERE menu_id = @maintenance_parent_id
UNION ALL
SELECT 
    '维护报表菜单:',
    menu_id,
    menu_name,
    parent_id,
    path,
    component,
    perms,
    menu_type
FROM sys_menu 
WHERE menu_id = @report_menu_id OR parent_id = @report_menu_id
ORDER BY menu_id;

-- 10. 显示配置的变量值
SELECT 
    @maintenance_parent_id as maintenance_parent_id,
    @report_menu_id as report_menu_id,
    @admin_role_id as admin_role_id;
