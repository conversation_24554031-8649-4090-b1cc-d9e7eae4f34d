# 库存消耗报表前端开发文档

## 项目概述

### 功能描述
库存消耗报表功能用于统计和分析生产过程中消耗品的使用量，提供耗材消耗效率分析、趋势分析、排行榜等功能，帮助企业优化库存管理和成本控制。

### 技术栈
- **前端框架**: Vue 3 + Element Plus
- **状态管理**: Vuex
- **路由管理**: Vue Router
- **HTTP客户端**: Axios
- **图表库**: ECharts
- **构建工具**: Vite
- **UI组件**: Element Plus

## 页面结构设计

### 主页面布局
```
库存消耗报表
├── 查询条件区域
├── 统计卡片区域
├── 图表展示区域
├── 数据表格区域
└── 操作按钮区域
```

### 页面组件划分
1. **主容器组件**: `ConsumptionReport.vue`
2. **查询条件组件**: `SearchForm.vue`
3. **统计卡片组件**: `StatisticsCards.vue`
4. **图表组件**: `ConsumptionCharts.vue`
5. **数据表格组件**: `ConsumptionTable.vue`
6. **排行榜组件**: `RankingList.vue`
7. **趋势分析组件**: `TrendAnalysis.vue`

## 目录结构

```
src/views/item/consumption-report/
├── index.vue                    # 主页面
├── components/
│   ├── SearchForm.vue          # 查询条件表单
│   ├── StatisticsCards.vue     # 统计卡片
│   ├── ConsumptionCharts.vue   # 消耗图表
│   ├── ConsumptionTable.vue    # 消耗数据表格
│   ├── RankingList.vue         # 排行榜列表
│   ├── TrendAnalysis.vue       # 趋势分析
│   └── OptimizationSuggestions.vue # 优化建议
├── api/
│   └── consumption-report.js   # API接口定义
└── utils/
    ├── chart-config.js         # 图表配置
    └── data-formatter.js       # 数据格式化工具
```

## API接口设计

### 接口基础配置
```javascript
// api/consumption-report.js
import request from '@/utils/request'

const API_BASE = '/item/consumption-report'

// 基础查询接口
export function getConsumptionList(params) {
  return request({
    url: `${API_BASE}/list`,
    method: 'post',
    data: params
  })
}

// 统计数据接口
export function getConsumptionStatistics(params) {
  return request({
    url: `${API_BASE}/statistics`,
    method: 'post',
    data: params
  })
}

// 趋势数据接口
export function getConsumptionTrend(params) {
  return request({
    url: `${API_BASE}/trend`,
    method: 'get',
    params: params
  })
}

// 效率排行榜接口
export function getEfficiencyRanking(params) {
  return request({
    url: `${API_BASE}/efficiency-ranking`,
    method: 'post',
    data: params,
    params: { limit: params.limit || 10 }
  })
}

// 消耗量排行榜接口
export function getConsumptionRanking(params) {
  return request({
    url: `${API_BASE}/consumption-ranking`,
    method: 'post',
    data: params,
    params: { limit: params.limit || 10 }
  })
}

// 图表数据接口
export function getChartData(params) {
  return request({
    url: `${API_BASE}/chart-data`,
    method: 'post',
    data: params
  })
}

// 优化建议接口
export function getOptimizationSuggestions(params) {
  return request({
    url: `${API_BASE}/optimization-suggestions`,
    method: 'get',
    params: params
  })
}

// 生成汇总数据接口
export function generateSummary(params) {
  return request({
    url: `${API_BASE}/generate-summary`,
    method: 'post',
    params: params
  })
}

// 导出数据接口
export function exportConsumptionReport(params) {
  return request({
    url: `${API_BASE}/export`,
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
```

## 数据模型定义

### 查询参数模型
```javascript
// 基础查询参数
const searchParams = {
  pageNum: 1,
  pageSize: 10,
  startDate: '',
  endDate: '',
  itemId: '',
  itemName: '',
  itemType: null,
  consumptionType: null,
  warehouseId: '',
  deptId: '',
  analysisPeriod: 'monthly', // daily, weekly, monthly, quarterly, yearly
  timeDimension: 3 // 1-日, 2-周, 3-月, 4-季, 5-年
}

// 统计查询参数
const statisticsParams = {
  startDate: '',
  endDate: '',
  analysisPeriod: 'monthly',
  groupBy: 'item' // item, warehouse, dept, type
}

// 趋势查询参数
const trendParams = {
  itemId: '',
  startDate: '',
  endDate: '',
  timeDimension: 3
}
```

### 响应数据模型
```javascript
// 消耗报表数据
const consumptionReportData = {
  itemId: '',
  itemName: '',
  itemCode: '',
  itemType: 1,
  itemTypeName: '',
  specModel: '',
  unit: '',
  totalConsumption: 0,
  totalAmount: 0,
  avgUnitPrice: 0,
  consumptionCount: 0,
  efficiencyScore: 0,
  efficiencyLevel: '',
  efficiencyLevelName: '',
  consumptionPerUnit: 0,
  savingsAmount: 0
}

// 统计数据
const statisticsData = {
  totalConsumption: 0,
  totalAmount: 0,
  avgEfficiencyScore: 0,
  totalSavingsAmount: 0,
  itemCount: 0,
  excellentCount: 0,
  goodCount: 0,
  averageCount: 0,
  poorCount: 0
}

// 趋势数据
const trendData = {
  date: '',
  dateLabel: '',
  consumption: 0,
  amount: 0,
  consumptionPerUnit: 0,
  efficiencyScore: 0
}
```

## 组件设计规范

### 1. 查询条件组件 (SearchForm.vue)
```vue
<template>
  <el-form :model="searchForm" :inline="true" class="search-form">
    <el-form-item label="时间范围">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        @change="handleDateChange"
      />
    </el-form-item>
    
    <el-form-item label="物品名称">
      <el-input
        v-model="searchForm.itemName"
        placeholder="请输入物品名称"
        clearable
      />
    </el-form-item>
    
    <el-form-item label="物品类型">
      <el-select v-model="searchForm.itemType" placeholder="请选择" clearable>
        <el-option label="消耗品" :value="1" />
        <el-option label="备品备件" :value="2" />
      </el-select>
    </el-form-item>
    
    <el-form-item label="消耗类型">
      <el-select v-model="searchForm.consumptionType" placeholder="请选择" clearable>
        <el-option label="生产消耗" :value="1" />
        <el-option label="维护消耗" :value="2" />
        <el-option label="领用消耗" :value="3" />
        <el-option label="其他消耗" :value="4" />
      </el-select>
    </el-form-item>
    
    <el-form-item label="分析周期">
      <el-select v-model="searchForm.analysisPeriod" placeholder="请选择">
        <el-option label="日度" value="daily" />
        <el-option label="周度" value="weekly" />
        <el-option label="月度" value="monthly" />
        <el-option label="季度" value="quarterly" />
        <el-option label="年度" value="yearly" />
      </el-select>
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="handleSearch">查询</el-button>
      <el-button @click="handleReset">重置</el-button>
      <el-button type="success" @click="handleExport">导出</el-button>
    </el-form-item>
  </el-form>
</template>
```

### 2. 统计卡片组件 (StatisticsCards.vue)
```vue
<template>
  <el-row :gutter="20" class="statistics-cards">
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-item">
          <div class="stat-value">{{ formatNumber(statistics.totalConsumption) }}</div>
          <div class="stat-label">总消耗量</div>
          <div class="stat-unit">件</div>
        </div>
      </el-card>
    </el-col>
    
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-item">
          <div class="stat-value">{{ formatCurrency(statistics.totalAmount) }}</div>
          <div class="stat-label">总消耗金额</div>
          <div class="stat-unit">元</div>
        </div>
      </el-card>
    </el-col>
    
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-item">
          <div class="stat-value">{{ statistics.avgEfficiencyScore.toFixed(1) }}</div>
          <div class="stat-label">平均效率评分</div>
          <div class="stat-unit">分</div>
        </div>
      </el-card>
    </el-col>
    
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-item">
          <div class="stat-value">{{ formatCurrency(statistics.totalSavingsAmount) }}</div>
          <div class="stat-label">节约金额</div>
          <div class="stat-unit">元</div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>
```

### 3. 图表组件 (ConsumptionCharts.vue)
```vue
<template>
  <el-row :gutter="20" class="chart-container">
    <!-- 消耗趋势图 -->
    <el-col :span="12">
      <el-card title="消耗趋势分析">
        <div ref="trendChart" class="chart" style="height: 300px;"></div>
      </el-card>
    </el-col>
    
    <!-- 消耗类型分布图 -->
    <el-col :span="12">
      <el-card title="消耗类型分布">
        <div ref="typeChart" class="chart" style="height: 300px;"></div>
      </el-card>
    </el-col>
    
    <!-- 效率分布图 -->
    <el-col :span="12">
      <el-card title="效率等级分布">
        <div ref="efficiencyChart" class="chart" style="height: 300px;"></div>
      </el-card>
    </el-col>
    
    <!-- 仓库消耗对比图 -->
    <el-col :span="12">
      <el-card title="仓库消耗对比">
        <div ref="warehouseChart" class="chart" style="height: 300px;"></div>
      </el-card>
    </el-col>
  </el-row>
</template>
```

## 样式设计规范

### 主题色彩
```scss
// 主色调
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 效率等级颜色
$excellent-color: #67C23A;  // 优秀 - 绿色
$good-color: #409EFF;       // 良好 - 蓝色
$average-color: #E6A23C;    // 一般 - 橙色
$poor-color: #F56C6C;       // 较差 - 红色

// 背景色
$bg-color: #f5f7fa;
$card-bg: #ffffff;
```

### 组件样式
```scss
.consumption-report {
  padding: 20px;
  background-color: $bg-color;
  
  .search-form {
    background: $card-bg;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
  }
  
  .statistics-cards {
    margin-bottom: 20px;
    
    .stat-card {
      text-align: center;
      
      .stat-value {
        font-size: 28px;
        font-weight: bold;
        color: $primary-color;
      }
      
      .stat-label {
        font-size: 14px;
        color: $info-color;
        margin-top: 8px;
      }
    }
  }
  
  .chart-container {
    margin-bottom: 20px;
    
    .chart {
      width: 100%;
      height: 300px;
    }
  }
  
  .data-table {
    background: $card-bg;
    border-radius: 4px;
    
    .efficiency-tag {
      &.excellent { background-color: $excellent-color; }
      &.good { background-color: $good-color; }
      &.average { background-color: $average-color; }
      &.poor { background-color: $poor-color; }
    }
  }
}
```

## 功能特性要求

### 1. 响应式设计
- 支持桌面端和平板端显示
- 图表自适应容器大小
- 表格支持横向滚动

### 2. 交互体验
- 查询条件实时验证
- 加载状态提示
- 错误信息友好显示
- 数据为空时的空状态提示

### 3. 性能优化
- 图表懒加载
- 数据分页加载
- 防抖搜索
- 缓存查询结果

### 4. 数据可视化
- 多维度图表展示
- 颜色编码效率等级
- 趋势线平滑处理
- 图表交互功能

## 开发优先级

### 第一阶段（核心功能）
1. 主页面布局和路由配置
2. 查询条件组件
3. 数据表格组件
4. 基础API接口集成

### 第二阶段（数据可视化）
1. 统计卡片组件
2. 趋势图表组件
3. 分布图表组件
4. 排行榜组件

### 第三阶段（高级功能）
1. 优化建议组件
2. 数据导出功能
3. 高级筛选功能
4. 性能优化

### 第四阶段（用户体验）
1. 响应式适配
2. 加载动画
3. 错误处理
4. 用户引导

## 测试要求

### 单元测试
- 组件渲染测试
- 方法功能测试
- 数据格式化测试

### 集成测试
- API接口调用测试
- 组件交互测试
- 路由跳转测试

### 用户体验测试
- 不同屏幕尺寸测试
- 网络异常处理测试
- 数据边界情况测试

## 部署说明

### 构建配置
```javascript
// vite.config.js
export default {
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'echarts': ['echarts'],
          'element-plus': ['element-plus']
        }
      }
    }
  }
}
```

### 环境变量
```bash
# .env.production
VITE_API_BASE_URL=http://your-api-domain.com
VITE_APP_TITLE=库存消耗报表系统
```

这个开发文档提供了完整的前端开发指导，包括技术选型、组件设计、API集成、样式规范等，可以作为前端开发的详细参考。
