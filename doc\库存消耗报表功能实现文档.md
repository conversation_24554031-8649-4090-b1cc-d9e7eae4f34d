# 库存消耗报表功能实现文档

## 一、功能概述

库存消耗报表功能是一个专门用于统计和分析生产过程中消耗品使用量、分析耗材消耗效率的报表系统。该功能基于现有的出库记录、领用记录等数据，通过数据汇总和分析算法，为管理层提供全面的消耗分析和效率评估。

## 二、功能特点

### 2.1 核心功能
- **消耗统计分析**: 统计各类物品的消耗量、消耗金额、消耗频次
- **效率评估**: 基于基准数据计算消耗效率，提供效率评分和等级
- **趋势分析**: 支持日、周、月、季度、年度等多维度趋势分析
- **对比分析**: 支持不同物品、仓库、部门间的消耗对比
- **预警功能**: 识别消耗异常和效率低下的物品
- **优化建议**: 基于分析结果提供消耗优化建议

### 2.2 数据来源
- **出库记录**: 基于item_outbound和item_outbound_detail表的出库数据
- **领用记录**: 基于item_requisition和item_requisition_detail表的领用数据
- **库存数据**: 结合item_inventory表的库存信息
- **基准数据**: 通过item_consumption_benchmark表设置的标准消耗基准

### 2.3 分析维度
- **消耗类型**: 生产消耗、维护消耗、领用消耗、其他消耗
- **时间维度**: 支持日、周、月、季度、年度分析
- **空间维度**: 按仓库、部门进行分组分析
- **物品维度**: 按物品类型、具体物品进行分析
- **效率维度**: 优秀、良好、一般、较差四个等级

## 三、数据库设计

### 3.1 核心数据表

#### 3.1.1 库存消耗记录汇总表 (item_consumption_summary)
用于存储按日期、物品、仓库、消耗类型汇总的消耗数据。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| summary_id | varchar(32) | 汇总记录ID（主键） |
| item_id | varchar(32) | 物品ID |
| warehouse_id | int(11) | 仓库ID |
| consumption_date | date | 消耗日期 |
| consumption_type | tinyint(4) | 消耗类型(1-生产,2-维护,3-领用,4-其他) |
| total_quantity | decimal(10,3) | 总消耗数量 |
| total_amount | decimal(10,2) | 总消耗金额 |
| avg_unit_price | decimal(8,2) | 平均单价 |
| consumption_count | int(11) | 消耗次数 |
| related_production_output | decimal(10,3) | 关联生产产量 |
| consumption_efficiency | decimal(8,4) | 消耗效率 |

#### 3.1.2 库存消耗效率分析表 (item_consumption_efficiency)
用于存储按周期计算的效率分析数据。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| efficiency_id | varchar(32) | 效率分析ID（主键） |
| item_id | varchar(32) | 物品ID |
| analysis_period | varchar(20) | 分析周期 |
| period_start_date | date | 周期开始日期 |
| period_end_date | date | 周期结束日期 |
| total_consumption | decimal(10,3) | 总消耗量 |
| total_production_output | decimal(10,3) | 总生产产量 |
| consumption_per_unit | decimal(8,4) | 单位产量消耗 |
| efficiency_score | decimal(5,2) | 效率评分(0-100) |
| efficiency_level | varchar(20) | 效率等级 |
| benchmark_consumption | decimal(8,4) | 基准消耗量 |
| efficiency_variance | decimal(8,4) | 效率偏差 |
| cost_per_unit | decimal(8,2) | 单位产量成本 |
| savings_amount | decimal(10,2) | 节约金额 |
| optimization_suggestions | text | 优化建议 |

#### 3.1.3 库存消耗基准数据表 (item_consumption_benchmark)
用于存储消耗基准和效率阈值配置。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| benchmark_id | varchar(32) | 基准数据ID（主键） |
| item_id | varchar(32) | 物品ID |
| benchmark_type | varchar(20) | 基准类型(standard/target/historical) |
| consumption_per_unit | decimal(8,4) | 单位产量标准消耗 |
| cost_per_unit | decimal(8,2) | 单位产量标准成本 |
| efficiency_threshold_excellent | decimal(8,4) | 优秀效率阈值 |
| efficiency_threshold_good | decimal(8,4) | 良好效率阈值 |
| efficiency_threshold_average | decimal(8,4) | 一般效率阈值 |
| valid_start_date | date | 有效开始日期 |
| valid_end_date | date | 有效结束日期 |
| is_active | tinyint(1) | 是否启用 |

### 3.2 数据视图

#### 3.2.1 消耗明细视图 (v_item_consumption_detail)
整合出库和领用数据的统一消耗明细视图，用于数据汇总和分析。

## 四、后端实现

### 4.1 模块结构
```
com.jingfang.item_consumption_report/
├── module/
│   ├── entity/                    # 实体类
│   │   ├── ItemConsumptionSummary.java
│   │   ├── ItemConsumptionEfficiency.java
│   │   └── ItemConsumptionBenchmark.java
│   ├── request/                   # 请求参数类
│   │   └── ItemConsumptionReportRequest.java
│   └── vo/                        # 响应对象类
│       ├── ItemConsumptionReportVo.java
│       └── ItemConsumptionStatisticsVo.java
├── mapper/                        # 数据访问层
│   └── ItemConsumptionReportMapper.java
├── service/                       # 业务逻辑层
│   ├── ItemConsumptionReportService.java
│   └── impl/
│       └── ItemConsumptionReportServiceImpl.java
└── resources/mapper/              # MyBatis映射文件
    └── ItemConsumptionReportMapper.xml
```

### 4.2 核心接口

#### 4.2.1 数据查询接口
- `POST /item/consumption-report/list` - 分页查询消耗报表数据
- `POST /item/consumption-report/statistics` - 查询消耗统计数据
- `GET /item/consumption-report/trend` - 查询消耗趋势数据
- `GET /item/consumption-report/type-distribution` - 查询消耗类型分布
- `POST /item/consumption-report/efficiency-ranking` - 查询效率排行榜
- `POST /item/consumption-report/consumption-ranking` - 查询消耗量排行榜

#### 4.2.2 图表数据接口
- `POST /item/consumption-report/chart-data` - 生成图表数据
- `POST /item/consumption-report/analysis-report` - 生成分析报告

#### 4.2.3 数据管理接口
- `POST /item/consumption-report/generate-summary` - 生成消耗汇总数据
- `POST /item/consumption-report/update-efficiency` - 更新效率分析数据
- `POST /item/consumption-report/batch-update-summary` - 批量更新汇总数据
- `POST /item/consumption-report/batch-calculate-efficiency` - 批量计算效率数据

#### 4.2.4 预警和建议接口
- `POST /item/consumption-report/alerts` - 获取消耗预警数据
- `GET /item/consumption-report/optimization-suggestions` - 获取优化建议

### 4.3 权限配置

系统定义了以下权限标识：
- `item:consumption:report:list` - 查看消耗报表列表
- `item:consumption:report:statistics` - 查看消耗统计数据
- `item:consumption:report:trend` - 查看消耗趋势分析
- `item:consumption:report:distribution` - 查看消耗分布分析
- `item:consumption:report:ranking` - 查看消耗排行榜
- `item:consumption:report:chart` - 查看图表数据
- `item:consumption:report:analysis` - 查看分析报告
- `item:consumption:report:export` - 导出报表数据
- `item:consumption:report:alert` - 查看预警信息
- `item:consumption:report:suggestion` - 查看优化建议
- `item:consumption:report:manage` - 管理报表数据

## 五、核心算法

### 5.1 消耗效率计算算法

效率评分基于实际消耗与基准消耗的对比：
```
效率评分 = 100 - (实际消耗偏差 / 基准消耗) * 100
效率等级 = 
  - 优秀(excellent): 效率评分 >= 90
  - 良好(good): 80 <= 效率评分 < 90  
  - 一般(average): 60 <= 效率评分 < 80
  - 较差(poor): 效率评分 < 60
```

### 5.2 节约金额计算算法

当实际消耗低于基准消耗时计算节约金额：
```
节约金额 = (基准消耗 - 实际消耗) * 生产产量 * 单位成本
```

### 5.3 趋势分析算法

支持多种时间维度的趋势分析：
- 日度趋势：按天统计消耗数据
- 周度趋势：按周汇总消耗数据  
- 月度趋势：按月汇总消耗数据
- 季度趋势：按季度汇总消耗数据
- 年度趋势：按年汇总消耗数据

## 六、图表支持

系统复用现有的Chart.js图表组件，支持以下图表类型：

### 6.1 趋势图表
- **折线图**: 展示消耗量、消耗金额、效率评分的时间趋势
- **多系列对比**: 支持多个指标在同一图表中对比显示

### 6.2 分布图表  
- **饼图**: 展示消耗类型分布、效率等级分布
- **环形图**: 展示仓库消耗占比、部门消耗占比

### 6.3 对比图表
- **柱状图**: 展示不同物品、仓库、部门的消耗对比
- **排行榜**: 展示消耗量排行、效率排行

## 七、使用说明

### 7.1 数据初始化
1. 执行SQL脚本创建相关数据表
2. 配置消耗基准数据
3. 运行数据汇总任务生成历史汇总数据

### 7.2 日常使用
1. 系统自动从出库和领用记录中提取消耗数据
2. 定期运行汇总任务更新消耗汇总数据
3. 定期运行效率分析任务计算效率指标
4. 通过报表界面查看分析结果和图表

### 7.3 数据维护
- 定期更新消耗基准数据
- 监控数据质量，确保消耗记录的准确性
- 根据业务变化调整效率评估算法

## 八、扩展建议

1. **与生产系统集成**: 获取实际生产产量数据，提高效率计算准确性
2. **机器学习优化**: 引入机器学习算法，提供更智能的消耗预测和优化建议
3. **移动端支持**: 开发移动端应用，支持现场消耗数据录入和查看
4. **实时监控**: 建立实时消耗监控系统，及时发现异常消耗
5. **成本分析**: 深化成本分析功能，支持更详细的成本核算和控制
