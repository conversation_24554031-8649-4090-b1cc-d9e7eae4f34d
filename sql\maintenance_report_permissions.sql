-- 维护报表功能权限配置SQL脚本

-- 1. 插入维护报表菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
-- 维护报表主菜单
(2100, '维护报表', 2000, 5, 'report', 'maintenance/report/index', '', 1, 0, 'C', '0', '0', 'maintenance:report:view', 'chart', 'admin', sysdate(), '', null, '维护报表菜单'),

-- 任务完成率报表子菜单
(2101, '任务完成率报表', 2100, 1, 'completion', 'maintenance/report/completion', '', 1, 0, 'C', '0', '0', 'maintenance:report:completion', 'rate', 'admin', sysdate(), '', null, '任务完成率报表'),

-- 故障响应时间报表子菜单
(2102, '故障响应时间报表', 2100, 2, 'response', 'maintenance/report/response', '', 1, 0, 'C', '0', '0', 'maintenance:report:response', 'time', 'admin', sysdate(), '', null, '故障响应时间报表'),

-- 维护成本分析报表子菜单
(2103, '维护成本分析报表', 2100, 3, 'cost', 'maintenance/report/cost', '', 1, 0, 'C', '0', '0', 'maintenance:report:cost', 'money', 'admin', sysdate(), '', null, '维护成本分析报表'),

-- 综合报表子菜单
(2104, '综合报表', 2100, 4, 'comprehensive', 'maintenance/report/comprehensive', '', 1, 0, 'C', '0', '0', 'maintenance:report:comprehensive', 'dashboard', 'admin', sysdate(), '', null, '综合报表'),

-- 报表配置子菜单
(2105, '报表配置', 2100, 5, 'config', 'maintenance/report/config', '', 1, 0, 'C', '0', '0', 'maintenance:report:config', 'tool', 'admin', sysdate(), '', null, '报表配置');

-- 2. 插入维护报表按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
-- 任务完成率报表按钮
(2111, '任务完成率查询', 2101, 1, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:completion:query', '#', 'admin', sysdate(), '', null, ''),
(2112, '任务完成率导出', 2101, 2, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:completion:export', '#', 'admin', sysdate(), '', null, ''),

-- 故障响应时间报表按钮
(2121, '故障响应时间查询', 2102, 1, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:response:query', '#', 'admin', sysdate(), '', null, ''),
(2122, '故障响应时间导出', 2102, 2, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:response:export', '#', 'admin', sysdate(), '', null, ''),

-- 维护成本分析报表按钮
(2131, '维护成本查询', 2103, 1, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:cost:query', '#', 'admin', sysdate(), '', null, ''),
(2132, '维护成本导出', 2103, 2, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:cost:export', '#', 'admin', sysdate(), '', null, ''),

-- 综合报表按钮
(2141, '综合报表查询', 2104, 1, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:comprehensive:query', '#', 'admin', sysdate(), '', null, ''),
(2142, '综合报表导出', 2104, 2, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:comprehensive:export', '#', 'admin', sysdate(), '', null, ''),

-- 图表相关按钮
(2151, '图表查看', 2100, 1, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:chart', '#', 'admin', sysdate(), '', null, ''),
(2152, '报表导出', 2100, 2, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:export', '#', 'admin', sysdate(), '', null, ''),

-- 报表配置按钮
(2161, '配置查询', 2105, 1, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:config:query', '#', 'admin', sysdate(), '', null, ''),
(2162, '配置新增', 2105, 2, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:config:add', '#', 'admin', sysdate(), '', null, ''),
(2163, '配置修改', 2105, 3, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:config:edit', '#', 'admin', sysdate(), '', null, ''),
(2164, '配置删除', 2105, 4, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:report:config:remove', '#', 'admin', sysdate(), '', null, '');

-- 3. 为管理员角色分配维护报表权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 主菜单权限
(1, 2100),
-- 子菜单权限
(1, 2101), (1, 2102), (1, 2103), (1, 2104), (1, 2105),
-- 按钮权限
(1, 2111), (1, 2112), (1, 2121), (1, 2122), (1, 2131), (1, 2132),
(1, 2141), (1, 2142), (1, 2151), (1, 2152), (1, 2161), (1, 2162),
(1, 2163), (1, 2164);

-- 4. 创建维护报表角色（可选）
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES
(100, '维护报表管理员', 'maintenance_report_admin', 5, '1', 1, 1, '0', '0', 'admin', sysdate(), '', null, '维护报表管理员角色'),
(101, '维护报表查看员', 'maintenance_report_viewer', 6, '2', 1, 1, '0', '0', 'admin', sysdate(), '', null, '维护报表查看员角色');

-- 5. 为维护报表管理员角色分配权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 维护报表管理员拥有所有权限
(100, 2100), (100, 2101), (100, 2102), (100, 2103), (100, 2104), (100, 2105),
(100, 2111), (100, 2112), (100, 2121), (100, 2122), (100, 2131), (100, 2132),
(100, 2141), (100, 2142), (100, 2151), (100, 2152), (100, 2161), (100, 2162),
(100, 2163), (100, 2164);

-- 6. 为维护报表查看员角色分配权限（只有查看权限）
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 维护报表查看员只有查看权限
(101, 2100), (101, 2101), (101, 2102), (101, 2103), (101, 2104),
(101, 2111), (101, 2121), (101, 2131), (101, 2141), (101, 2151);

-- 7. 插入字典数据
-- 报表类型字典
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES
(100, '维护报表类型', 'maintenance_report_type', '0', 'admin', sysdate(), '', null, '维护报表类型列表');

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1000, 1, '任务完成率报表', '1', 'maintenance_report_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '任务完成率报表'),
(1001, 2, '故障响应时间报表', '2', 'maintenance_report_type', '', 'info', 'N', '0', 'admin', sysdate(), '', null, '故障响应时间报表'),
(1002, 3, '维护成本分析报表', '3', 'maintenance_report_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '维护成本分析报表'),
(1003, 4, '综合报表', '4', 'maintenance_report_type', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '综合报表');

-- 时间维度字典
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES
(101, '报表时间维度', 'report_time_dimension', '0', 'admin', sysdate(), '', null, '报表时间维度列表');

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1010, 1, '日', '1', 'report_time_dimension', '', '', 'N', '0', 'admin', sysdate(), '', null, '按日统计'),
(1011, 2, '周', '2', 'report_time_dimension', '', '', 'N', '0', 'admin', sysdate(), '', null, '按周统计'),
(1012, 3, '月', '3', 'report_time_dimension', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '按月统计'),
(1013, 4, '季', '4', 'report_time_dimension', '', '', 'N', '0', 'admin', sysdate(), '', null, '按季度统计'),
(1014, 5, '年', '5', 'report_time_dimension', '', '', 'N', '0', 'admin', sysdate(), '', null, '按年统计');

-- 成本类型字典
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES
(102, '维护成本类型', 'maintenance_cost_type', '0', 'admin', sysdate(), '', null, '维护成本类型列表');

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1020, 1, '人工成本', '1', 'maintenance_cost_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '人工成本'),
(1021, 2, '材料成本', '2', 'maintenance_cost_type', '', 'info', 'N', '0', 'admin', sysdate(), '', null, '材料成本'),
(1022, 3, '外包成本', '3', 'maintenance_cost_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '外包成本'),
(1023, 4, '停机成本', '4', 'maintenance_cost_type', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '停机成本');

-- 图表类型字典
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES
(103, '图表类型', 'chart_type', '0', 'admin', sysdate(), '', null, '图表类型列表');

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1030, 1, '折线图', 'line', 'chart_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '折线图'),
(1031, 2, '柱状图', 'bar', 'chart_type', '', 'info', 'N', '0', 'admin', sysdate(), '', null, '柱状图'),
(1032, 3, '饼图', 'pie', 'chart_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '饼图'),
(1033, 4, '混合图', 'mixed', 'chart_type', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '混合图');
