-- 测试消耗量排行榜查询SQL
-- 用于验证修复后的SQL语句是否正确

-- 1. 测试基本的消耗量排行榜查询（不带过滤条件）
SELECT
    i.item_id,
    i.item_name,
    i.item_code,
    i.item_type,
    CASE i.item_type
        WHEN 1 THEN '消耗品'
        WHEN 2 THEN '备品备件'
        ELSE '未知'
    END as item_type_name,
    i.spec_model,
    i.unit,
    COALESCE(SUM(cs.total_quantity), 0) as total_consumption,
    COALESCE(SUM(cs.total_amount), 0) as total_amount,
    CASE WHEN SUM(cs.total_quantity) > 0
         THEN SUM(cs.total_amount) / SUM(cs.total_quantity)
         ELSE 0
    END as avg_unit_price,
    COALESCE(SUM(cs.consumption_count), 0) as consumption_count
FROM item_base_info i
INNER JOIN item_consumption_summary cs ON i.item_id = cs.item_id
WHERE i.deleted = 0
GROUP BY i.item_id
ORDER BY total_consumption DESC
LIMIT 5;

-- 2. 测试带时间条件的消耗量排行榜查询
SELECT
    i.item_id,
    i.item_name,
    i.item_code,
    i.item_type,
    CASE i.item_type
        WHEN 1 THEN '消耗品'
        WHEN 2 THEN '备品备件'
        ELSE '未知'
    END as item_type_name,
    i.spec_model,
    i.unit,
    COALESCE(SUM(cs.total_quantity), 0) as total_consumption,
    COALESCE(SUM(cs.total_amount), 0) as total_amount,
    CASE WHEN SUM(cs.total_quantity) > 0
         THEN SUM(cs.total_amount) / SUM(cs.total_quantity)
         ELSE 0
    END as avg_unit_price,
    COALESCE(SUM(cs.consumption_count), 0) as consumption_count
FROM item_base_info i
INNER JOIN item_consumption_summary cs ON i.item_id = cs.item_id
WHERE i.deleted = 0
    AND cs.consumption_date BETWEEN '2024-01-01' AND '2024-12-31'
GROUP BY i.item_id
ORDER BY total_consumption DESC
LIMIT 5;

-- 3. 测试带消耗类型过滤的消耗量排行榜查询
SELECT
    i.item_id,
    i.item_name,
    i.item_code,
    i.item_type,
    CASE i.item_type
        WHEN 1 THEN '消耗品'
        WHEN 2 THEN '备品备件'
        ELSE '未知'
    END as item_type_name,
    i.spec_model,
    i.unit,
    COALESCE(SUM(cs.total_quantity), 0) as total_consumption,
    COALESCE(SUM(cs.total_amount), 0) as total_amount,
    CASE WHEN SUM(cs.total_quantity) > 0
         THEN SUM(cs.total_amount) / SUM(cs.total_quantity)
         ELSE 0
    END as avg_unit_price,
    COALESCE(SUM(cs.consumption_count), 0) as consumption_count
FROM item_base_info i
INNER JOIN item_consumption_summary cs ON i.item_id = cs.item_id
WHERE i.deleted = 0
    AND cs.consumption_date BETWEEN '2024-01-01' AND '2024-12-31'
    AND cs.consumption_type IN (1, 3)  -- 生产消耗和领用消耗
GROUP BY i.item_id
ORDER BY total_consumption DESC
LIMIT 5;

-- 4. 检查相关表是否存在数据
SELECT 'item_base_info' as table_name, COUNT(*) as record_count FROM item_base_info WHERE deleted = 0
UNION ALL
SELECT 'item_consumption_summary' as table_name, COUNT(*) as record_count FROM item_consumption_summary
UNION ALL
SELECT 'item_consumption_summary_with_date_filter' as table_name, COUNT(*) as record_count 
FROM item_consumption_summary 
WHERE consumption_date BETWEEN '2024-01-01' AND '2024-12-31';

-- 5. 检查消耗类型分布
SELECT 
    consumption_type,
    CASE consumption_type
        WHEN 1 THEN '生产消耗'
        WHEN 2 THEN '维护消耗'
        WHEN 3 THEN '领用消耗'
        WHEN 4 THEN '其他消耗'
        ELSE '未知'
    END as consumption_type_name,
    COUNT(*) as record_count,
    SUM(total_quantity) as total_quantity,
    SUM(total_amount) as total_amount
FROM item_consumption_summary
GROUP BY consumption_type
ORDER BY consumption_type;
