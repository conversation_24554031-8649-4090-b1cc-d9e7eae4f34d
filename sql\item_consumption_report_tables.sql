-- 库存消耗报表功能相关数据表创建脚本
-- 执行日期：2025-01-16

-- 1. 库存消耗记录汇总表
DROP TABLE IF EXISTS `item_consumption_summary`;
CREATE TABLE `item_consumption_summary` (
  `summary_id` varchar(32) NOT NULL COMMENT '汇总记录ID',
  `item_id` varchar(32) NOT NULL COMMENT '物品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `consumption_date` date NOT NULL COMMENT '消耗日期',
  `consumption_type` tinyint(4) NOT NULL COMMENT '消耗类型(1-生产消耗,2-维护消耗,3-领用消耗,4-其他消耗)',
  `total_quantity` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '总消耗数量',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总消耗金额',
  `avg_unit_price` decimal(8,2) DEFAULT NULL COMMENT '平均单价',
  `consumption_count` int(11) NOT NULL DEFAULT '0' COMMENT '消耗次数',
  `related_production_output` decimal(10,3) DEFAULT NULL COMMENT '关联生产产量',
  `consumption_efficiency` decimal(8,4) DEFAULT NULL COMMENT '消耗效率(单位产量消耗)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`summary_id`),
  UNIQUE KEY `uk_item_warehouse_date_type` (`item_id`, `warehouse_id`, `consumption_date`, `consumption_type`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_warehouse_id` (`warehouse_id`),
  KEY `idx_consumption_date` (`consumption_date`),
  KEY `idx_consumption_type` (`consumption_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存消耗记录汇总表';

-- 2. 库存消耗效率分析表
DROP TABLE IF EXISTS `item_consumption_efficiency`;
CREATE TABLE `item_consumption_efficiency` (
  `efficiency_id` varchar(32) NOT NULL COMMENT '效率分析ID',
  `item_id` varchar(32) NOT NULL COMMENT '物品ID',
  `analysis_period` varchar(20) NOT NULL COMMENT '分析周期(daily/weekly/monthly/quarterly)',
  `period_start_date` date NOT NULL COMMENT '周期开始日期',
  `period_end_date` date NOT NULL COMMENT '周期结束日期',
  `total_consumption` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '总消耗量',
  `total_production_output` decimal(10,3) DEFAULT NULL COMMENT '总生产产量',
  `consumption_per_unit` decimal(8,4) DEFAULT NULL COMMENT '单位产量消耗',
  `efficiency_score` decimal(5,2) DEFAULT NULL COMMENT '效率评分(0-100)',
  `efficiency_level` varchar(20) DEFAULT NULL COMMENT '效率等级(excellent/good/average/poor)',
  `benchmark_consumption` decimal(8,4) DEFAULT NULL COMMENT '基准消耗量',
  `efficiency_variance` decimal(8,4) DEFAULT NULL COMMENT '效率偏差',
  `cost_per_unit` decimal(8,2) DEFAULT NULL COMMENT '单位产量成本',
  `savings_amount` decimal(10,2) DEFAULT NULL COMMENT '节约金额',
  `optimization_suggestions` text COMMENT '优化建议',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`efficiency_id`),
  UNIQUE KEY `uk_item_period` (`item_id`, `analysis_period`, `period_start_date`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_analysis_period` (`analysis_period`),
  KEY `idx_period_dates` (`period_start_date`, `period_end_date`),
  KEY `idx_efficiency_level` (`efficiency_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存消耗效率分析表';

-- 3. 库存消耗基准数据表
DROP TABLE IF EXISTS `item_consumption_benchmark`;
CREATE TABLE `item_consumption_benchmark` (
  `benchmark_id` varchar(32) NOT NULL COMMENT '基准数据ID',
  `item_id` varchar(32) NOT NULL COMMENT '物品ID',
  `benchmark_type` varchar(20) NOT NULL COMMENT '基准类型(standard/target/historical)',
  `consumption_per_unit` decimal(8,4) NOT NULL COMMENT '单位产量标准消耗',
  `cost_per_unit` decimal(8,2) DEFAULT NULL COMMENT '单位产量标准成本',
  `efficiency_threshold_excellent` decimal(8,4) DEFAULT NULL COMMENT '优秀效率阈值',
  `efficiency_threshold_good` decimal(8,4) DEFAULT NULL COMMENT '良好效率阈值',
  `efficiency_threshold_average` decimal(8,4) DEFAULT NULL COMMENT '一般效率阈值',
  `valid_start_date` date NOT NULL COMMENT '有效开始日期',
  `valid_end_date` date DEFAULT NULL COMMENT '有效结束日期',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0-否,1-是)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`benchmark_id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_benchmark_type` (`benchmark_type`),
  KEY `idx_valid_dates` (`valid_start_date`, `valid_end_date`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存消耗基准数据表';

-- 4. 创建消耗汇总数据的视图
CREATE OR REPLACE VIEW `v_item_consumption_detail` AS
SELECT
    CONCAT('OUT_', od.detail_id) as consumption_id,
    od.item_id,
    od.warehouse_id,
    DATE(o.business_date) as consumption_date,
    CASE o.outbound_type
        WHEN 1 THEN 1  -- 生产领用 -> 生产消耗
        WHEN 2 THEN 3  -- 部门领用 -> 领用消耗
        WHEN 3 THEN 2  -- 维护领用 -> 维护消耗
        ELSE 4         -- 其他 -> 其他消耗
    END as consumption_type,
    od.quantity as consumption_quantity,
    od.amount as consumption_amount,
    od.unit_price,
    o.outbound_id as source_id,
    'outbound' as source_type,
    o.recipient_name,
    o.recipient_dept,
    od.remark,
    o.create_time
FROM item_outbound_detail od
INNER JOIN item_outbound o ON od.outbound_id = o.outbound_id
WHERE o.status = 4  -- 只统计已审核通过的出库单
  AND (o.del_flag = '0' OR o.del_flag IS NULL)  -- 排除已删除的记录

UNION ALL

SELECT
    CONCAT('REQ_', rd.detail_id) as consumption_id,
    rd.item_id,
    rd.warehouse_id,
    DATE(r.create_time) as consumption_date,
    3 as consumption_type,  -- 领用消耗
    rd.actual_quantity as consumption_quantity,
    (rd.actual_quantity * COALESCE(i.unit_price, 0)) as consumption_amount,
    COALESCE(i.unit_price, 0) as unit_price,
    r.requisition_id as source_id,
    'requisition' as source_type,
    u.nick_name as recipient_name,
    d.dept_name as recipient_dept,
    rd.remark,
    r.create_time
FROM item_requisition_detail rd
INNER JOIN item_requisition r ON rd.requisition_id = r.requisition_id
LEFT JOIN sys_user u ON r.applicant_id = u.user_id
LEFT JOIN sys_dept d ON r.dept_id = d.dept_id
LEFT JOIN (
    SELECT item_id, AVG(unit_price) as unit_price
    FROM item_outbound_detail
    WHERE unit_price > 0
    GROUP BY item_id
) i ON rd.item_id = i.item_id
WHERE r.status = 6  -- 只统计已完成的领用单
  AND (r.del_flag = '0' OR r.del_flag IS NULL);  -- 排除已删除的记录

-- 5. 插入一些基准数据示例（可选）
INSERT INTO `item_consumption_benchmark`
(`benchmark_id`, `item_id`, `benchmark_type`, `consumption_per_unit`, `cost_per_unit`,
 `efficiency_threshold_excellent`, `efficiency_threshold_good`, `efficiency_threshold_average`,
 `valid_start_date`, `is_active`, `remark`, `create_by`)
VALUES
('BENCH_DEFAULT_001', 'DEFAULT', 'standard', 1.0000, 10.00, 0.8000, 1.0000, 1.2000,
 '2025-01-01', 1, '默认基准数据，适用于所有未设置专门基准的物品', 'system'),
('BENCH_DEFAULT_002', 'DEFAULT', 'target', 0.9000, 9.00, 0.7000, 0.9000, 1.1000,
 '2025-01-01', 1, '目标基准数据，用于设定改进目标', 'system');
