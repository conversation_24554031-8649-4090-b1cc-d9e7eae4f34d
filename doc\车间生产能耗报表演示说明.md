# 车间生产能耗报表演示说明

## 📋 概述

本文档介绍了车间生产能耗报表的功能特性和使用方法。该报表是一个演示页面，使用模拟数据展示车间能耗管理的各项功能。

## 🎯 功能特性

### 1. 数据筛选功能
- **车间选择**: 支持按车间筛选能耗数据
  - 冲压车间
  - 焊装车间  
  - 涂装车间
  - 总装车间
  - 发动机车间

- **时间范围**: 支持自定义日期范围查询
- **能耗类型**: 支持按能耗类型筛选
  - 电力消耗
  - 燃气消耗
  - 蒸汽消耗
  - 水消耗

### 2. 统计卡片展示
显示关键能耗指标：
- **总能耗**: 显示总体能耗量和趋势
- **平均单耗**: 单位产量能耗指标
- **能效等级**: 当前能效水平评级
- **节能率**: 节能效果百分比

### 3. 图表分析功能

#### 能耗趋势分析
- 支持日、周、月三种时间维度
- 多能耗类型趋势对比
- 交互式图表展示

#### 能耗类型分布
- 饼图展示各类型能耗占比
- 直观显示能耗结构

#### 车间能耗对比
- 支持三种对比模式：
  - 总能耗对比
  - 单位产量能耗对比
  - 能效比对比

### 4. 详细数据表格
- 分页显示详细能耗数据
- 支持数据导出功能
- 能效等级标签显示

## 🚀 访问方式

### 路由配置
页面路由已配置为：`/energy/consumption-report`

### 菜单结构
```
能耗管理
└── 车间生产能耗报表
```

## 💡 技术实现

### 前端技术栈
- **Vue.js 2.x**: 主框架
- **Element UI**: UI组件库
- **ECharts**: 图表库
- **SCSS**: 样式预处理器

### 组件结构
```
consumption-report.vue
├── 筛选条件区域
├── 统计卡片区域
├── 图表展示区域
│   ├── 能耗趋势图
│   ├── 能耗分布图
│   └── 车间对比图
└── 数据表格区域
```

### 数据模拟
- 使用JavaScript生成模拟数据
- 模拟真实的车间生产场景
- 包含完整的能耗指标数据

## 📊 数据说明

### 模拟数据范围
- **电力消耗**: 20,000 - 70,000 kWh
- **燃气消耗**: 2,000 - 7,000 m³
- **蒸汽消耗**: 50 - 150 t
- **水消耗**: 100 - 300 t
- **产量**: 5,000 - 15,000 件

### 能效等级
- **A级**: 优秀能效水平
- **B级**: 良好能效水平
- **C级**: 一般能效水平

## 🎨 界面特性

### 响应式设计
- 支持桌面端、平板端、移动端
- 自适应布局调整

### 交互体验
- 图表支持缩放、平移操作
- 表格支持排序、筛选
- 实时数据更新

### 视觉设计
- 清晰的数据可视化
- 统一的色彩主题
- 直观的图标设计

## 🔧 使用说明

### 1. 基本查询
1. 选择要查询的车间
2. 设置时间范围
3. 选择能耗类型（可选）
4. 点击"搜索"按钮

### 2. 图表交互
- **趋势图**: 点击图例可显示/隐藏数据系列
- **分布图**: 悬停查看详细数据
- **对比图**: 切换对比模式查看不同维度数据

### 3. 数据导出
- 点击"导出"按钮
- 支持Excel格式导出
- 包含当前筛选条件下的所有数据

### 4. 页面刷新
- 点击右上角刷新按钮
- 重新加载最新数据

## 📈 扩展功能

### 可扩展特性
- 支持接入真实数据源
- 可配置更多能耗类型
- 支持自定义报表模板
- 可集成预警功能

### 集成建议
- 与设备监控系统集成
- 连接ERP生产数据
- 对接能源管理系统
- 集成成本核算模块

## 🎯 应用场景

### 管理决策
- 能耗成本分析
- 生产效率评估
- 节能改进方案制定

### 日常监控
- 实时能耗监控
- 异常情况预警
- 趋势分析预测

### 绩效考核
- 车间能耗KPI
- 节能目标达成率
- 能效改进效果评估

## 📝 注意事项

1. **数据说明**: 当前使用模拟数据，仅供演示
2. **权限控制**: 可根据需要配置访问权限
3. **性能优化**: 大数据量时建议分页加载
4. **浏览器兼容**: 建议使用现代浏览器访问

## 🔄 后续开发

### 计划功能
- [ ] 接入真实数据接口
- [ ] 增加预警功能
- [ ] 支持报表定制
- [ ] 移动端优化
- [ ] 数据钻取功能

---

**文档版本**: V1.0  
**创建日期**: 2025-01-16  
**适用范围**: 演示环境  
**最后更新**: 2025-01-16
