# 库存消耗报表SQL修复说明

## 问题描述

在测试查询消耗趋势数据接口时出现以下SQL错误：

```
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'monitor_system.cs.consumption_date' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
### The error may exist in file [D:\devCodeRepo\20250320134733\device_monitor\device_module\target\classes\mapper\ItemConsumptionReportMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
```

## 问题原因

这个错误是由于MySQL的`sql_mode=only_full_group_by`模式导致的。在这个模式下：

1. **严格模式要求**: SELECT列表中的非聚合列必须出现在GROUP BY子句中
2. **函数依赖性**: 非聚合列必须在功能上依赖于GROUP BY子句中的列
3. **标准SQL兼容**: 这是为了符合SQL标准的要求

### 原始问题SQL

```sql
SELECT
    cs.consumption_date as date,  -- 问题1：这个列没有在所有GROUP BY子句中
    DATE_FORMAT(cs.consumption_date, '%Y-%m') as dateLabel,  -- 问题2：dateLabel中的cs.consumption_date也不在GROUP BY中
    COALESCE(SUM(cs.total_quantity), 0) as consumption,
    COALESCE(SUM(cs.total_amount), 0) as amount,
    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
FROM item_consumption_summary cs
LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
WHERE cs.item_id = #{itemId}
GROUP BY YEAR(cs.consumption_date), MONTH(cs.consumption_date)  -- 问题：没有包含cs.consumption_date
ORDER BY YEAR(cs.consumption_date), MONTH(cs.consumption_date)
```

### 具体错误信息

**第一次错误**（Expression #1）：
```
Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'monitor_system.cs.consumption_date'
```

**第二次错误**（Expression #2）：
```
Expression #2 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'monitor_system.cs.consumption_date'
```

这表明`cs.consumption_date`在多个地方被使用但都不符合GROUP BY要求。

## 解决方案

### 修复策略

采用分条件查询的方式，为不同的时间维度使用不同的SQL语句：

1. **日度趋势 (timeDimension = 1)**: 直接使用`cs.consumption_date`进行GROUP BY
2. **其他维度**: 使用`MIN(cs.consumption_date)`作为代表日期

### 修复后的SQL

#### 1. 日度趋势查询
```sql
SELECT
    cs.consumption_date as date,  -- 直接使用，在GROUP BY中包含
    DATE_FORMAT(cs.consumption_date, '%Y-%m-%d') as dateLabel,
    COALESCE(SUM(cs.total_quantity), 0) as consumption,
    COALESCE(SUM(cs.total_amount), 0) as amount,
    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
FROM item_consumption_summary cs
LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
    AND cs.consumption_date BETWEEN e.period_start_date AND e.period_end_date
WHERE cs.item_id = #{itemId}
    AND cs.consumption_date BETWEEN #{startDate} AND #{endDate}
GROUP BY cs.consumption_date  -- 包含所有非聚合列
ORDER BY cs.consumption_date
```

#### 2. 月度趋势查询
```sql
SELECT
    MIN(cs.consumption_date) as date,  -- 使用聚合函数
    DATE_FORMAT(MIN(cs.consumption_date), '%Y-%m') as dateLabel,  -- 修复：对MIN结果使用DATE_FORMAT
    COALESCE(SUM(cs.total_quantity), 0) as consumption,
    COALESCE(SUM(cs.total_amount), 0) as amount,
    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
FROM item_consumption_summary cs
LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
    AND cs.consumption_date BETWEEN e.period_start_date AND e.period_end_date
WHERE cs.item_id = #{itemId}
    AND cs.consumption_date BETWEEN #{startDate} AND #{endDate}
GROUP BY YEAR(cs.consumption_date), MONTH(cs.consumption_date)  -- 符合GROUP BY要求
ORDER BY YEAR(cs.consumption_date), MONTH(cs.consumption_date)
```

#### 3. 其他维度查询
类似地，周度、季度、年度查询都使用`MIN(cs.consumption_date)`作为代表日期，并且在所有使用日期函数的地方都对`MIN(cs.consumption_date)`进行操作：

- **周度**: `CONCAT(YEAR(MIN(cs.consumption_date)), '-W', WEEK(MIN(cs.consumption_date)))`
- **季度**: `CONCAT(YEAR(MIN(cs.consumption_date)), '-Q', QUARTER(MIN(cs.consumption_date)))`
- **年度**: `DATE_FORMAT(MIN(cs.consumption_date), '%Y')`

### 关键修复点

1. **date字段**: 使用`MIN(cs.consumption_date)`替代`cs.consumption_date`
2. **dateLabel字段**: 对`MIN(cs.consumption_date)`应用日期函数，而不是直接对`cs.consumption_date`
3. **一致性**: 确保所有非聚合列都使用聚合函数或出现在GROUP BY子句中

## 修复文件

### 修改的文件
- `device_module/src/main/resources/mapper/ItemConsumptionReportMapper.xml`

### 修改的查询方法
- `selectConsumptionTrend` - 查询消耗趋势数据

### 修改内容
将原来的单一SQL查询改为使用`<choose>`标签的多条件查询，每个时间维度使用独立的SQL语句。

## 验证方法

### 1. SQL直接验证
使用提供的测试SQL脚本：
```bash
mysql -u username -p database_name < sql/test_consumption_trend_sql.sql
```

### 2. 接口测试验证
使用PowerShell测试脚本：
```powershell
powershell -ExecutionPolicy Bypass -File "test-trend-api-fix.ps1"
```

### 3. 检查MySQL模式
```sql
SELECT @@sql_mode;
```

## 最佳实践

### 1. GROUP BY规则
- 确保SELECT列表中的非聚合列都出现在GROUP BY子句中
- 或者使用聚合函数（如MIN、MAX、ANY_VALUE）处理非聚合列

### 2. 时间维度处理
- 对于需要按时间分组的查询，明确指定分组的时间函数
- 使用代表性日期（如MIN、MAX）来获取分组的时间点

### 3. 兼容性考虑
- 编写SQL时考虑MySQL的严格模式
- 测试时使用与生产环境相同的sql_mode设置

## 影响范围

### 已修复
✅ `selectConsumptionTrend` - 消耗趋势查询（GROUP BY问题）
✅ `selectEfficiencyRanking` - 消耗效率排行榜查询（SQL语法问题）
✅ `selectConsumptionRanking` - 消耗量排行榜查询（SQL语法问题）

### 需要检查
🔍 其他可能存在类似问题的查询：
- 图表数据查询（已检查，无问题）
- 统计数据查询（已检查，无问题）

## 第二个问题：SQL语法错误

### 问题描述
在测试查询消耗效率排行榜接口时出现SQL语法错误：

```
You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'NULLAND e.period_start_date >= '2024-01-01 00:00:00'' at line 29
```

### 问题原因
错误信息显示`'NULLAND`，这表明在SQL拼接时缺少了空格。问题出现在MyBatis XML中的条件拼接处。

### 解决方案
在`selectEfficiencyRanking`查询中，确保所有条件之间有正确的空格分隔：

**修复前**:
```xml
AND e.period_start_date &gt;= #{request.startDate}
```

**修复后**:
```xml
AND e.period_start_date &gt;= #{request.startDate}
```

添加了行尾空格，确保与下一行条件正确拼接。

## 第三个问题：消耗量排行榜SQL语法错误

### 问题描述
在测试查询消耗量排行榜接口时出现类似的SQL语法错误：

```
You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'cs.consumption_date BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 00:00:00' GROU' at line 21
```

### 问题原因
与效率排行榜相同的问题：`<where>`标签在处理静态条件和动态条件混合时出现SQL拼接问题。

### 解决方案
同样将`<where>`标签改为直接的`WHERE`子句：

**修复前**:
```xml
<where>
    i.deleted = 0
    <if test="request.startDate != null and request.endDate != null">
        AND cs.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
    </if>
</where>
```

**修复后**:
```xml
WHERE i.deleted = 0
    <if test="request.startDate != null and request.endDate != null">
        AND cs.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
    </if>
```

### 修复的查询方法
- `selectConsumptionRanking` - 查询消耗量排行榜

## 总结

通过将单一的动态SQL查询拆分为多个针对不同时间维度的独立查询，成功解决了MySQL `only_full_group_by`模式的兼容性问题。这种修复方式：

1. **符合SQL标准**: 满足MySQL严格模式的要求
2. **保持功能完整**: 不影响原有的查询功能
3. **提高可维护性**: 每个时间维度的逻辑更加清晰
4. **增强兼容性**: 适用于不同的MySQL配置

修复后的代码可以在任何MySQL配置下正常运行，无需修改数据库的sql_mode设置。
