# 故障申报表名变更说明

## 变更概述

将故障申报功能的数据库表名从 `fault_report` 更改为 `asset_fault_report`，以更好地体现该表与资产管理的关联性。

## 变更详情

### 1. 数据库表名变更

**原表名**: `fault_report`  
**新表名**: `asset_fault_report`

### 2. 修改的文件列表

#### 2.1 SQL脚本文件
- `sql/fault_report.sql`
  - 表创建语句中的表名
  - 插入测试数据的表名
  - 表注释更新为"资产故障申报表"

#### 2.2 Java实体类
- `device_module/src/main/java/com/jingfang/fault_report/module/entity/FaultReport.java`
  - @TableName注解值更新
  - 类注释更新

#### 2.3 DTO和VO类
- `device_module/src/main/java/com/jingfang/fault_report/module/dto/FaultReportDto.java`
  - 类注释更新为"资产故障申报DTO"
- `device_module/src/main/java/com/jingfang/fault_report/module/vo/FaultReportVo.java`
  - 类注释更新为"资产故障申报VO"

#### 2.4 请求类
- `device_module/src/main/java/com/jingfang/fault_report/module/request/FaultReportSearchRequest.java`
  - 类注释更新为"资产故障申报查询请求"

#### 2.5 Mapper接口和XML
- `device_module/src/main/java/com/jingfang/fault_report/mapper/FaultReportMapper.java`
  - 接口注释更新为"资产故障申报Mapper接口"
- `device_module/src/main/resources/mapper/fault_report/FaultReportMapper.xml`
  - 所有SQL语句中的表名从 `fault_report` 更新为 `asset_fault_report`

#### 2.6 Service层
- `device_module/src/main/java/com/jingfang/fault_report/service/FaultReportService.java`
  - 接口注释更新为"资产故障申报服务接口"
- `device_module/src/main/java/com/jingfang/fault_report/service/impl/FaultReportServiceImpl.java`
  - 类注释更新为"资产故障申报服务实现类"

#### 2.7 Controller层
- `device_monitor-admin/src/main/java/com/jingfang/web/controller/maintenance/FaultReportController.java`
  - 类注释更新为"资产故障申报控制器"
- `device_monitor-admin/src/main/java/com/jingfang/web/controller/maintenance/FaultReportTestController.java`
  - 类注释更新为"资产故障申报测试控制器"

#### 2.8 文档文件
- `doc/故障申报功能接口文档.md`
  - 文档标题和描述更新
  - 添加数据库表名说明
- `doc/微信小程序维护模块需求文档.md`
  - 相关描述保持一致

### 3. 新增文件

#### 3.1 表名更新脚本
- `sql/update_fault_report_table_name.sql`
  - 用于在现有数据库中安全地重命名表
  - 包含备份提醒和验证逻辑

#### 3.2 变更说明文档
- `doc/故障申报表名变更说明.md` (本文档)

## 部署指南

### 新部署环境

对于新的部署环境，直接执行更新后的SQL脚本：

```sql
-- 执行更新后的建表脚本
source sql/fault_report.sql;
```

### 现有环境升级

对于已经存在 `fault_report` 表的环境，需要执行表名更新脚本：

```sql
-- 1. 备份现有数据（重要！）
CREATE TABLE fault_report_backup AS SELECT * FROM fault_report;

-- 2. 执行表名更新脚本
source sql/update_fault_report_table_name.sql;

-- 3. 验证数据完整性
SELECT COUNT(*) FROM asset_fault_report;
```

### 应用程序更新

1. **停止应用服务**
2. **更新代码**：部署包含表名变更的新版本代码
3. **执行数据库脚本**：根据环境选择相应的SQL脚本
4. **启动应用服务**
5. **功能验证**：使用测试接口验证功能正常

## 验证步骤

### 1. 数据库验证

```sql
-- 检查表是否存在
SHOW TABLES LIKE '%fault%';

-- 检查表结构
DESCRIBE asset_fault_report;

-- 检查数据
SELECT COUNT(*) FROM asset_fault_report;
```

### 2. 应用程序验证

使用测试接口验证功能：

```bash
# 检查基础功能
GET /maintenance/fault/test/check

# 测试故障申报提交
POST /maintenance/fault/test/submit

# 测试查询功能
GET /maintenance/fault/test/my-reports
```

## 注意事项

1. **数据备份**：在执行表名更改前，务必备份现有数据
2. **停机时间**：表名更改需要短暂的停机时间
3. **权限检查**：确保数据库用户有重命名表的权限
4. **依赖检查**：确认没有其他系统直接依赖原表名
5. **监控日志**：更新后密切监控应用日志，确保无异常

## 回滚方案

如果需要回滚到原表名：

```sql
-- 回滚表名
RENAME TABLE asset_fault_report TO fault_report;

-- 恢复原代码版本
-- 重启应用服务
```

## 影响评估

### 正面影响
- 表名更加语义化，体现与资产管理的关联
- 符合系统整体命名规范
- 便于后续功能扩展

### 风险评估
- 低风险：仅涉及表名变更，不影响表结构和数据
- 需要协调的停机时间较短
- 代码变更范围明确，影响可控

## 完成确认

变更完成后，请确认以下检查项：

- [ ] 数据库表名已更新为 `asset_fault_report`
- [ ] 所有相关代码文件已更新
- [ ] 应用程序启动正常
- [ ] 测试接口验证通过
- [ ] 功能测试正常
- [ ] 日志无异常错误
- [ ] 文档已更新

---

**变更执行人**: AI Assistant  
**变更时间**: 2025-04-01  
**审核状态**: 待审核
