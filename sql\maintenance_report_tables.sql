-- 维护报表功能相关数据表创建脚本

-- 1. 维护成本记录表
DROP TABLE IF EXISTS `maintenance_cost_record`;
CREATE TABLE `maintenance_cost_record` (
  `cost_id` varchar(32) NOT NULL COMMENT '成本记录ID',
  `task_id` varchar(32) DEFAULT NULL COMMENT '维护任务ID',
  `fault_id` varchar(32) DEFAULT NULL COMMENT '故障申报ID',
  `asset_id` varchar(32) DEFAULT NULL COMMENT '资产ID',
  `cost_type` tinyint(4) NOT NULL COMMENT '成本类型(1-人工成本,2-材料成本,3-外包成本,4-停机成本)',
  `cost_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '成本金额',
  `cost_description` text COMMENT '成本说明',
  `labor_hours` decimal(8,2) DEFAULT NULL COMMENT '人工工时(小时)',
  `hourly_rate` decimal(8,2) DEFAULT NULL COMMENT '小时费率',
  `material_quantity` decimal(10,3) DEFAULT NULL COMMENT '材料数量',
  `unit_price` decimal(8,2) DEFAULT NULL COMMENT '材料单价',
  `downtime_hours` decimal(8,2) DEFAULT NULL COMMENT '停机时间(小时)',
  `downtime_loss_rate` decimal(8,2) DEFAULT NULL COMMENT '停机损失率(元/小时)',
  `vendor_name` varchar(100) DEFAULT NULL COMMENT '外包供应商名称',
  `record_time` datetime NOT NULL COMMENT '记录时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除(0-否,1-是)',
  PRIMARY KEY (`cost_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_fault_id` (`fault_id`),
  KEY `idx_asset_id` (`asset_id`),
  KEY `idx_record_time` (`record_time`),
  KEY `idx_cost_type` (`cost_type`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='维护成本记录表';

-- 2. 维护报表配置表
DROP TABLE IF EXISTS `maintenance_report_config`;
CREATE TABLE `maintenance_report_config` (
  `config_id` varchar(32) NOT NULL COMMENT '配置ID',
  `report_name` varchar(100) NOT NULL COMMENT '报表名称',
  `report_type` tinyint(4) NOT NULL COMMENT '报表类型(1-完成率,2-响应时间,3-成本分析,4-综合报表)',
  `time_dimension` tinyint(4) NOT NULL COMMENT '时间维度(1-日,2-周,3-月,4-季,5-年)',
  `filter_config` json COMMENT '筛选条件配置',
  `chart_config` json COMMENT '图表配置',
  `is_default` tinyint(4) DEFAULT '0' COMMENT '是否默认配置(0-否,1-是)',
  `is_public` tinyint(4) DEFAULT '0' COMMENT '是否公共配置(0-否,1-是)',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除(0-否,1-是)',
  PRIMARY KEY (`config_id`),
  KEY `idx_report_type` (`report_type`),
  KEY `idx_create_by` (`create_by`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='维护报表配置表';

-- 3. 插入默认报表配置数据
INSERT INTO `maintenance_report_config` (`config_id`, `report_name`, `report_type`, `time_dimension`, `filter_config`, `chart_config`, `is_default`, `is_public`, `sort_order`, `status`, `create_by`) VALUES
('RPT_COMPLETION_MONTHLY', '月度任务完成率报表', 1, 3, '{"deptId": "", "assetType": "", "priority": ""}', '{"chartType": "line", "showLegend": true, "colors": ["#409EFF", "#67C23A", "#E6A23C"]}', 1, 1, 1, 1, 'system'),
('RPT_RESPONSE_WEEKLY', '周度故障响应时间报表', 2, 2, '{"faultType": "", "urgencyLevel": "", "handlerId": ""}', '{"chartType": "bar", "showLegend": true, "colors": ["#F56C6C", "#E6A23C", "#409EFF"]}', 1, 1, 2, 1, 'system'),
('RPT_COST_QUARTERLY', '季度维护成本分析报表', 3, 4, '{"deptId": "", "assetId": "", "costType": ""}', '{"chartType": "pie", "showLegend": true, "colors": ["#409EFF", "#67C23A", "#E6A23C", "#F56C6C"]}', 1, 1, 3, 1, 'system'),
('RPT_COMPREHENSIVE_MONTHLY', '月度综合维护报表', 4, 3, '{"deptId": "", "assetType": "", "includeCompleted": true}', '{"chartType": "mixed", "showLegend": true, "colors": ["#409EFF", "#67C23A", "#E6A23C", "#F56C6C", "#909399"]}', 1, 1, 4, 1, 'system');

-- 4. 创建维护成本记录视图（用于统计查询优化）
DROP VIEW IF EXISTS `v_maintenance_cost_summary`;
CREATE VIEW `v_maintenance_cost_summary` AS
SELECT
    mcr.asset_id,
    a.asset_name,
    a.asset_id as asset_code,
    a.dept_id,
    d.dept_name,
    DATE_FORMAT(mcr.record_time, '%Y-%m') as record_month,
    DATE_FORMAT(mcr.record_time, '%Y-%u') as record_week,
    DATE_FORMAT(mcr.record_time, '%Y-%m-%d') as record_date,
    mcr.cost_type,
    CASE mcr.cost_type
        WHEN 1 THEN '人工成本'
        WHEN 2 THEN '材料成本'
        WHEN 3 THEN '外包成本'
        WHEN 4 THEN '停机成本'
        ELSE '其他成本'
    END as cost_type_name,
    SUM(mcr.cost_amount) as total_cost,
    COUNT(*) as record_count,
    AVG(mcr.cost_amount) as avg_cost
FROM maintenance_cost_record mcr
LEFT JOIN asset_ledger a ON mcr.asset_id = a.asset_id
LEFT JOIN sys_dept d ON a.dept_id = d.dept_id
WHERE mcr.deleted = 0
GROUP BY mcr.asset_id, a.asset_name, a.asset_id, a.dept_id, d.dept_name,
         DATE_FORMAT(mcr.record_time, '%Y-%m'), DATE_FORMAT(mcr.record_time, '%Y-%u'),
         DATE_FORMAT(mcr.record_time, '%Y-%m-%d'), mcr.cost_type;

-- 5. 创建维护任务统计视图（用于完成率分析）
DROP VIEW IF EXISTS `v_maintenance_task_summary`;
CREATE VIEW `v_maintenance_task_summary` AS
SELECT
    mt.asset_id,
    a.asset_name,
    a.asset_id as asset_code,
    a.dept_id,
    d.dept_name,
    DATE_FORMAT(mt.scheduled_time, '%Y-%m') as scheduled_month,
    DATE_FORMAT(mt.scheduled_time, '%Y-%u') as scheduled_week,
    DATE_FORMAT(mt.scheduled_time, '%Y-%m-%d') as scheduled_date,
    mt.priority,
    CASE mt.priority
        WHEN 1 THEN '低'
        WHEN 2 THEN '中'
        WHEN 3 THEN '高'
        WHEN 4 THEN '紧急'
        ELSE '未知'
    END as priority_name,
    COUNT(*) as total_tasks,
    SUM(CASE WHEN mt.status = 7 THEN 1 ELSE 0 END) as completed_tasks,
    SUM(CASE WHEN mt.status IN (1,2,3,4) AND mt.scheduled_time < NOW() THEN 1 ELSE 0 END) as overdue_tasks,
    SUM(CASE WHEN mt.status = 7 AND mt.actual_end_time <= mt.scheduled_time THEN 1 ELSE 0 END) as ontime_completed_tasks,
    AVG(CASE WHEN mt.status = 7 AND mt.actual_start_time IS NOT NULL AND mt.actual_end_time IS NOT NULL
             THEN TIMESTAMPDIFF(HOUR, mt.actual_start_time, mt.actual_end_time)
             ELSE NULL END) as avg_completion_hours
FROM maintenance_task mt
LEFT JOIN asset_ledger a ON mt.asset_id = a.asset_id
LEFT JOIN sys_dept d ON a.dept_id = d.dept_id
WHERE mt.deleted = 0
GROUP BY mt.asset_id, a.asset_name, a.asset_id, a.dept_id, d.dept_name,
         DATE_FORMAT(mt.scheduled_time, '%Y-%m'), DATE_FORMAT(mt.scheduled_time, '%Y-%u'),
         DATE_FORMAT(mt.scheduled_time, '%Y-%m-%d'), mt.priority;

-- 6. 创建故障响应时间统计视图
DROP VIEW IF EXISTS `v_fault_response_summary`;
CREATE VIEW `v_fault_response_summary` AS
SELECT
    fr.asset_id,
    a.asset_name,
    a.asset_id as asset_code,
    a.dept_id,
    d.dept_name,
    DATE_FORMAT(fr.report_time, '%Y-%m') as report_month,
    DATE_FORMAT(fr.report_time, '%Y-%u') as report_week,
    DATE_FORMAT(fr.report_time, '%Y-%m-%d') as report_date,
    fr.fault_type,
    CASE fr.fault_type
        WHEN 1 THEN '电气故障'
        WHEN 2 THEN '机械故障'
        WHEN 3 THEN '控制系统故障'
        WHEN 4 THEN '安全故障'
        WHEN 5 THEN '其他故障'
        ELSE '未知故障'
    END as fault_type_name,
    fr.urgency_level,
    CASE fr.urgency_level
        WHEN 1 THEN '一般'
        WHEN 2 THEN '较急'
        WHEN 3 THEN '紧急'
        WHEN 4 THEN '特急'
        ELSE '未知'
    END as urgency_level_name,
    COUNT(*) as total_faults,
    SUM(CASE WHEN fr.status >= 2 THEN 1 ELSE 0 END) as accepted_faults,
    SUM(CASE WHEN fr.status = 4 THEN 1 ELSE 0 END) as completed_faults,
    AVG(CASE WHEN fr.accept_time IS NOT NULL
             THEN TIMESTAMPDIFF(HOUR, fr.report_time, fr.accept_time)
             ELSE NULL END) as avg_response_hours,
    AVG(CASE WHEN fr.actual_complete_time IS NOT NULL AND fr.accept_time IS NOT NULL
             THEN TIMESTAMPDIFF(HOUR, fr.accept_time, fr.actual_complete_time)
             ELSE NULL END) as avg_processing_hours
FROM asset_fault_report fr
LEFT JOIN asset_ledger a ON fr.asset_id = a.asset_id
LEFT JOIN sys_dept d ON a.dept_id = d.dept_id
WHERE fr.deleted = 0
GROUP BY fr.asset_id, a.asset_name, a.asset_id, a.dept_id, d.dept_name,
         DATE_FORMAT(fr.report_time, '%Y-%m'), DATE_FORMAT(fr.report_time, '%Y-%u'),
         DATE_FORMAT(fr.report_time, '%Y-%m-%d'), fr.fault_type, fr.urgency_level;
