# 微信小程序维护模块需求文档

## 1. 概述

本文档描述微信小程序端维护模块的功能需求和接口规范。维护模块作为小程序首页功能菜单中的一项，主要提供故障申报和维护任务的查看执行功能，帮助用户快速申报设备故障，维护人员在移动端高效完成设备维护工作。

### 1.1 主要功能

- **故障申报**：用户可以快速申报设备故障，支持拍照记录
- **维护任务**：查看和执行分配的维护任务
- **进度跟踪**：实时查看故障处理和维护任务进度
- **扫码功能**：支持扫描资产二维码快速定位设备

## 2. 页面结构

```
小程序首页
└── 功能菜单
    └── 维护模块
        ├── 故障申报页
        ├── 我的故障申报列表页
        ├── 故障申报详情页
        ├── 维护任务列表页
        ├── 任务详情页
        ├── 任务执行页
        └── 资产扫码页（可选）
```

## 3. 功能模块详细说明

### 3.1 故障申报页

**功能描述**：
- 用户可以快速申报设备故障
- 支持扫码选择故障设备
- 支持拍照记录故障现象
- 支持选择故障类型和紧急程度
- 自动生成故障处理任务

**接口信息**：

#### 3.1.1 提交故障申报

- **接口URL**: `/maintenance/fault/report`
- **请求方式**: POST
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| faultReportDto | Object | 是 | 故障申报数据对象 |

faultReportDto对象结构:

```json
{
  "assetId": "**********",
  "assetName": "中央空调主机",
  "faultTitle": "空调制冷效果差",
  "faultDescription": "空调运行正常但制冷效果明显下降，室内温度无法降到设定温度",
  "faultType": 2,
  "urgencyLevel": 3,
  "reportLocation": "设备楼3层机房",
  "contactPhone": "13800138000",
  "images": ["image1.jpg", "image2.jpg"]
}
```

- **返回结果示例**:

```json
{
  "code": 200,
  "msg": "故障申报成功",
  "data": {
    "faultId": "FT20250401001",
    "taskId": "MT20250401003"
  }
}
```

#### 3.1.2 获取故障类型选项

- **接口URL**: `/maintenance/fault/types`
- **请求方式**: GET
- **返回结果示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {"value": 1, "label": "电气故障"},
    {"value": 2, "label": "机械故障"},
    {"value": 3, "label": "控制系统故障"},
    {"value": 4, "label": "安全故障"},
    {"value": 5, "label": "其他故障"}
  ]
}
```

#### 3.1.3 获取紧急程度选项

- **接口URL**: `/maintenance/fault/urgency-levels`
- **请求方式**: GET
- **返回结果示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {"value": 1, "label": "一般", "description": "不影响正常使用"},
    {"value": 2, "label": "较急", "description": "影响部分功能"},
    {"value": 3, "label": "紧急", "description": "严重影响使用"},
    {"value": 4, "label": "特急", "description": "存在安全隐患"}
  ]
}
```

### 3.2 我的故障申报列表页

**功能描述**：
- 查看用户提交的故障申报记录
- 查看故障处理进度和状态
- 支持按状态筛选故障记录

**接口信息**：

#### 3.2.1 查询我的故障申报

- **接口URL**: `/maintenance/fault/my-reports`
- **请求方式**: GET
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| status | Integer | 否 | 故障状态：1-已提交，2-已受理，3-处理中，4-已完成，5-已关闭 |
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 页大小，默认10 |

- **返回结果示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 15,
    "records": [
      {
        "faultId": "FT20250401001",
        "faultTitle": "空调制冷效果差",
        "assetName": "中央空调主机",
        "assetLocation": "设备楼3层机房",
        "faultType": 2,
        "faultTypeName": "机械故障",
        "urgencyLevel": 3,
        "urgencyLevelName": "紧急",
        "status": 3,
        "statusName": "处理中",
        "reportTime": "2025-04-01 09:30:00",
        "handlerName": "李工",
        "estimatedCompleteTime": "2025-04-01 16:00:00"
      }
    ]
  }
}
```

#### 3.2.2 查询故障申报详情

- **接口URL**: `/maintenance/fault/{faultId}`
- **请求方式**: GET
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| faultId | String | 是 | 故障ID |

- **返回结果示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "faultId": "FT20250401001",
    "faultTitle": "空调制冷效果差",
    "faultDescription": "空调运行正常但制冷效果明显下降，室内温度无法降到设定温度",
    "assetId": "**********",
    "assetName": "中央空调主机",
    "assetCode": "KT-2023-001",
    "assetLocation": "设备楼3层机房",
    "faultType": 2,
    "faultTypeName": "机械故障",
    "urgencyLevel": 3,
    "urgencyLevelName": "紧急",
    "status": 3,
    "statusName": "处理中",
    "reportLocation": "设备楼3层机房",
    "contactPhone": "13800138000",
    "reportTime": "2025-04-01 09:30:00",
    "reporterName": "张三",
    "handlerId": 105,
    "handlerName": "李工",
    "acceptTime": "2025-04-01 10:00:00",
    "estimatedCompleteTime": "2025-04-01 16:00:00",
    "actualCompleteTime": null,
    "handleDescription": "已检查制冷系统，发现冷凝器结垢严重，正在清洗处理",
    "images": ["image1.jpg", "image2.jpg"],
    "taskId": "MT20250401003"
  }
}
```

### 3.3 维护任务列表页

**功能描述**：
- 显示当前用户的所有维护任务
- 支持按任务状态筛选
- 支持任务标题搜索
- 紧急任务和即将到期任务特殊标识

**接口信息**：

#### 3.1.1 查询我的任务

- **接口URL**: `/maintenance/task/myTasks`
- **请求方式**: GET
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| statusList | Array | 否 | 任务状态列表，如[1,2,3]，不传则查询所有状态 |

- **返回结果示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "taskId": "MT20250401001",
      "taskTitle": "空调系统季度维护",
      "assetName": "中央空调主机",
      "assetCode": "KT-2023-001",
      "scheduledTime": "2025-04-15 09:00:00",
      "priority": 3,
      "priorityName": "高",
      "status": 1,
      "statusName": "待执行",
      "overdue": false
    },
    {
      "taskId": "MT20250401002",
      "taskTitle": "电梯月度安全检查",
      "assetName": "1号电梯",
      "assetCode": "DT-2022-001",
      "scheduledTime": "2025-04-10 14:00:00",
      "priority": 4,
      "priorityName": "紧急",
      "status": 2,
      "statusName": "执行中",
      "overdue": true
    }
  ]
}
```

### 3.2 任务详情页

**功能描述**：
- 显示任务完整信息
- 显示所需备品备件列表
- 根据任务状态显示不同的操作按钮

**接口信息**：

#### 3.2.1 获取任务详情

- **接口URL**: `/maintenance/task/{taskId}`
- **请求方式**: GET
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| taskId | String | 是 | 任务ID |

- **返回结果示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": "MT20250401001",
    "planId": "MP20250301001",
    "planName": "空调系统季度维护计划",
    "taskTitle": "空调系统季度维护",
    "assetId": "**********",
    "assetName": "中央空调主机",
    "assetCode": "KT-2023-001",
    "assetLocation": "设备楼3层机房",
    "maintenanceItems": "1. 检查制冷系统压力\n2. 清洗过滤器\n3. 检查电气控制系统\n4. 测试运行状态",
    "scheduledTime": "2025-04-15 09:00:00",
    "actualStartTime": null,
    "actualEndTime": null,
    "responsibleType": 1,
    "responsibleTypeName": "个人",
    "responsibleId": 103,
    "responsibleName": "张工",
    "executorId": null,
    "executorName": null,
    "status": 1,
    "statusName": "待执行",
    "priority": 3,
    "priorityName": "高",
    "checkResult": null,
    "resultDescription": null,
    "problemDescription": null,
    "solution": null,
    "overdue": false,
    "partList": [
      {
        "recordId": "TP20250401001",
        "partId": "PT20240001",
        "partName": "空调过滤网",
        "specModel": "KT-FW-001",
        "unit": "个",
        "plannedQuantity": 2,
        "actualQuantity": null,
        "currentStock": 10,
        "useStatus": 1,
        "useStatusName": "计划使用"
      },
      {
        "recordId": "TP20250401002",
        "partId": "PT20240002",
        "partName": "制冷剂",
        "specModel": "R410A",
        "unit": "kg",
        "plannedQuantity": 1.5,
        "actualQuantity": null,
        "currentStock": 5,
        "useStatus": 1,
        "useStatusName": "计划使用"
      }
    ]
  }
}
```

### 3.3 任务执行页

**功能描述**：
- 开始执行任务
- 记录维护过程（文字描述、照片）
- 记录备品备件使用情况
- 提交任务结果

**接口信息**：

#### 3.3.1 开始执行任务

- **接口URL**: `/maintenance/task/start/{taskId}`
- **请求方式**: POST
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| taskId | String | 是 | 任务ID |

- **返回结果示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": true
}
```

#### 3.3.2 保存草稿

- **接口URL**: `/maintenance/task/draft`
- **请求方式**: POST
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| taskDto | Object | 是 | 任务数据对象 |

taskDto对象结构:

```json
{
  "taskId": "MT20250401001",
  "checkResult": "正常",
  "resultDescription": "设备运行正常，已完成所有检查项目",
  "problemDescription": "发现冷凝器有轻微结垢",
  "solution": "已清洗处理",
  "partList": [
    {
      "recordId": "TP20250401001",
      "actualQuantity": 2,
      "useStatus": 2
    },
    {
      "recordId": "TP20250401002",
      "actualQuantity": 1,
      "useStatus": 2
    }
  ]
}
```

- **返回结果示例**:

```json
{
  "code": 200,
  "msg": "草稿保存成功",
  "data": true
}
```

#### 3.3.3 提交任务结果

- **接口URL**: `/maintenance/task/submit`
- **请求方式**: POST
- **请求参数**: 同保存草稿接口

- **返回结果示例**:

```json
{
  "code": 200,
  "msg": "任务提交成功",
  "data": true
}
```

### 3.4 资产扫码页（可选功能）

**功能描述**：
- 扫描资产二维码
- 查看该资产的维护任务

**接口信息**：

#### 3.4.1 根据资产ID查询任务

- **接口URL**: `/maintenance/task/list`
- **请求方式**: GET
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| assetId | String | 是 | 资产ID |

- **返回结果示例**: 同查询我的任务接口

## 4. 数据字典

### 4.1 任务状态(status)

| 状态码 | 状态名称 |
|-------|---------|
| 1 | 待执行 |
| 2 | 执行中 |
| 3 | 草稿 |
| 4 | 待审核 |
| 5 | 审核通过 |
| 6 | 审核不通过 |
| 7 | 已完成 |
| 8 | 已取消 |

### 4.2 任务优先级(priority)

| 优先级码 | 优先级名称 |
|---------|----------|
| 1 | 低 |
| 2 | 中 |
| 3 | 高 |
| 4 | 紧急 |

### 4.3 负责人类型(responsibleType)

| 类型码 | 类型名称 |
|-------|---------|
| 1 | 个人 |
| 2 | 部门 |

### 4.4 备品备件使用状态(useStatus)

| 状态码 | 状态名称 |
|-------|---------|
| 1 | 计划使用 |
| 2 | 已使用 |
| 3 | 未使用 |

### 4.5 故障类型(faultType)

| 类型码 | 类型名称 |
|-------|---------|
| 1 | 电气故障 |
| 2 | 机械故障 |
| 3 | 控制系统故障 |
| 4 | 安全故障 |
| 5 | 其他故障 |

### 4.6 故障紧急程度(urgencyLevel)

| 紧急程度码 | 紧急程度名称 | 描述 |
|----------|------------|------|
| 1 | 一般 | 不影响正常使用 |
| 2 | 较急 | 影响部分功能 |
| 3 | 紧急 | 严重影响使用 |
| 4 | 特急 | 存在安全隐患 |

### 4.7 故障状态(faultStatus)

| 状态码 | 状态名称 |
|-------|---------|
| 1 | 已提交 |
| 2 | 已受理 |
| 3 | 处理中 |
| 4 | 已完成 |
| 5 | 已关闭 |

## 5. UI设计建议

### 5.1 任务列表页
- 使用卡片式布局，每个任务一个卡片
- 不同优先级用不同颜色标识
- 逾期任务用红色边框突出显示
- 顶部添加状态筛选标签

### 5.2 任务详情页
- 分区域显示：基本信息区、维护事项区、备品备件区
- 底部固定操作按钮
- 重要信息突出显示

### 5.3 任务执行页
- 分步骤引导用户完成操作
- 拍照功能支持多张照片
- 表单验证，确保必填项完整

### 5.4 故障申报页
- 简洁明了的申报表单
- 扫码选择设备功能
- 故障类型和紧急程度选择器
- 拍照记录故障现象
- 一键提交申报

### 5.5 故障申报列表页
- 卡片式布局显示故障记录
- 不同状态用不同颜色标识
- 紧急故障突出显示
- 支持状态筛选
