-- 维护报表功能菜单配置SQL脚本
-- 适用于RuoYi框架的sys_menu表

-- 1. 首先查找设备维护父菜单的menu_id
-- 假设设备维护菜单的menu_id为2000（请根据实际情况调整）

-- 2. 添加维护报表主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
(
    '维护报表',                    -- 菜单名称
    2000,                         -- 父菜单ID（设备维护菜单ID，请根据实际调整）
    6,                            -- 显示顺序
    'report',                     -- 路由地址
    'maintenance/report/index',   -- 组件路径
    '',                           -- 路由参数
    1,                            -- 是否为外链（1是 0否）
    0,                            -- 是否缓存（0缓存 1不缓存）
    'C',                          -- 菜单类型（M目录 C菜单 F按钮）
    '0',                          -- 显示状态（0显示 1隐藏）
    '0',                          -- 菜单状态（0正常 1停用）
    'maintenance:report:view',    -- 权限标识
    's-data',                     -- 菜单图标
    'admin',                      -- 创建者
    NOW(),                        -- 创建时间
    'admin',                      -- 更新者
    NOW(),                        -- 更新时间
    '维护报表菜单'                 -- 备注
);

-- 3. 获取刚插入的维护报表菜单ID（用于后续按钮权限配置）
SET @report_menu_id = LAST_INSERT_ID();

-- 4. 添加维护报表相关的按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
-- 查看权限
(
    '报表查看',
    @report_menu_id,
    1,
    '',
    '',
    '',
    1,
    0,
    'F',
    '0',
    '0',
    'maintenance:report:view',
    '#',
    'admin',
    NOW(),
    'admin',
    NOW(),
    '维护报表查看权限'
),
-- 导出权限
(
    '报表导出',
    @report_menu_id,
    2,
    '',
    '',
    '',
    1,
    0,
    'F',
    '0',
    '0',
    'maintenance:report:export',
    '#',
    'admin',
    NOW(),
    'admin',
    NOW(),
    '维护报表导出权限'
),
-- 统计权限
(
    '数据统计',
    @report_menu_id,
    3,
    '',
    '',
    '',
    1,
    0,
    'F',
    '0',
    '0',
    'maintenance:report:stats',
    '#',
    'admin',
    NOW(),
    'admin',
    NOW(),
    '维护报表统计权限'
);

-- 5. 为管理员角色分配维护报表权限
-- 假设管理员角色ID为1（请根据实际情况调整）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE 'maintenance:report:%';

-- 6. 查询验证菜单配置
SELECT 
    menu_id,
    menu_name,
    parent_id,
    order_num,
    path,
    component,
    menu_type,
    visible,
    status,
    perms,
    icon,
    remark
FROM sys_menu 
WHERE menu_name = '维护报表' OR parent_id = @report_menu_id
ORDER BY parent_id, order_num;
