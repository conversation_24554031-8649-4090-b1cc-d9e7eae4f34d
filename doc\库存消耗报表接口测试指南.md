# 库存消耗报表接口测试指南

## 测试环境
- **服务地址**: http://localhost:8080
- **Token**: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A

## 测试接口列表

### 1. 获取效率优化建议（无权限验证）
**接口**: `GET /item/consumption-report/optimization-suggestions`

**测试URL**: 
```
http://localhost:8080/item/consumption-report/optimization-suggestions?itemId=TEST_ITEM&analysisPeriod=monthly
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A
```

**预期响应**:
```json
{
  "msg": "操作成功",
  "code": 200,
  "data": "基于当前消耗数据分析，建议：\n1. 定期检查库存消耗模式，识别异常消耗\n2. 优化采购计划，减少库存积压\n3. 加强消耗品使用培训，提高使用效率\n4. 建立消耗品使用标准，规范使用流程\n"
}
```

### 2. 生成消耗汇总数据（无权限验证）
**接口**: `POST /item/consumption-report/generate-summary`

**测试URL**: 
```
http://localhost:8080/item/consumption-report/generate-summary?startDate=2024-01-01&endDate=2024-12-31
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A
```

**预期响应**:
```json
{
  "msg": "生成消耗汇总数据成功，生成X条记录",
  "code": 200
}
```

### 3. 分页查询消耗报表数据（需要权限）
**接口**: `POST /item/consumption-report/list`

**测试URL**: 
```
http://localhost:8080/item/consumption-report/list
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A
Content-Type: application/json
```

**请求体**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "startDate": "2024-01-01",
  "endDate": "2024-12-31"
}
```

### 4. 查询消耗统计数据（需要权限）
**接口**: `POST /item/consumption-report/statistics`

**测试URL**: 
```
http://localhost:8080/item/consumption-report/statistics
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A
Content-Type: application/json
```

**请求体**:
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "analysisPeriod": "monthly"
}
```

### 5. 生成图表数据（需要权限）
**接口**: `POST /item/consumption-report/chart-data`

**测试URL**: 
```
http://localhost:8080/item/consumption-report/chart-data
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A
Content-Type: application/json
```

**请求体**:
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "timeDimension": 3
}
```

### 6. 查询消耗趋势数据（需要权限）
**接口**: `GET /item/consumption-report/trend`

**测试URL**: 
```
http://localhost:8080/item/consumption-report/trend?itemId=TEST_ITEM&startDate=2024-01-01&endDate=2024-12-31&timeDimension=3
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A
```

### 7. 查询消耗类型分布数据（需要权限）
**接口**: `GET /item/consumption-report/type-distribution`

**测试URL**: 
```
http://localhost:8080/item/consumption-report/type-distribution?itemId=TEST_ITEM&startDate=2024-01-01&endDate=2024-12-31
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A
```

### 8. 查询消耗效率排行榜（需要权限）
**接口**: `POST /item/consumption-report/efficiency-ranking`

**测试URL**: 
```
http://localhost:8080/item/consumption-report/efficiency-ranking?limit=5
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A
Content-Type: application/json
```

**请求体**:
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "analysisPeriod": "monthly"
}
```

### 9. 查询消耗量排行榜（需要权限）
**接口**: `POST /item/consumption-report/consumption-ranking`

**测试URL**: 
```
http://localhost:8080/item/consumption-report/consumption-ranking?limit=5
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A
Content-Type: application/json
```

**请求体**:
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-12-31"
}
```

### 10. 生成消耗分析报告（需要权限）
**接口**: `POST /item/consumption-report/analysis-report`

**测试URL**: 
```
http://localhost:8080/item/consumption-report/analysis-report
```

**请求头**:
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A
Content-Type: application/json
```

**请求体**:
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "analysisPeriod": "monthly",
  "includeEfficiencyAnalysis": true,
  "includeTrendAnalysis": true
}
```

## 测试步骤

### 使用Postman测试
1. 打开Postman
2. 创建新的请求
3. 设置请求方法和URL
4. 在Headers中添加Authorization头
5. 如果是POST请求，在Body中选择raw和JSON格式，添加请求体
6. 发送请求查看响应

### 使用浏览器测试（仅GET请求）
1. 打开浏览器
2. 在地址栏输入测试URL
3. 由于浏览器无法直接设置Authorization头，GET请求可能会返回认证失败

### 使用curl命令测试
```bash
# 测试获取优化建议
curl -X GET "http://localhost:8080/item/consumption-report/optimization-suggestions?itemId=TEST_ITEM&analysisPeriod=monthly" \
  -H "Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"

# 测试生成汇总数据
curl -X POST "http://localhost:8080/item/consumption-report/generate-summary?startDate=2024-01-01&endDate=2024-12-31" \
  -H "Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"
```

## 预期结果

### 成功响应格式
```json
{
  "msg": "操作成功",
  "code": 200,
  "data": { ... }
}
```

### 权限不足响应
```json
{
  "msg": "请求访问：/item/consumption-report/xxx，认证失败，无法访问系统资源",
  "code": 401
}
```

### 接口不存在响应
```json
{
  "timestamp": "2025-01-16T10:00:00.000+00:00",
  "status": 404,
  "error": "Not Found",
  "path": "/item/consumption-report/xxx"
}
```

## 测试重点

1. **接口可访问性**: 确认接口路径正确，能够被Spring Boot扫描到
2. **权限验证**: 确认权限配置是否正确
3. **参数验证**: 确认请求参数格式和验证逻辑
4. **业务逻辑**: 确认Service和Mapper是否正常工作
5. **数据返回**: 确认返回数据格式和内容是否正确

## 故障排查

如果接口测试失败，请检查：

1. **服务启动**: 确认后端服务正常启动，端口8080可访问
2. **包扫描**: 确认Controller被Spring Boot正确扫描
3. **依赖注入**: 确认Service和Mapper被正确注入
4. **数据库连接**: 确认数据库连接正常，相关表已创建
5. **权限配置**: 确认权限验证逻辑是否正确
6. **日志查看**: 查看后端日志，确认具体错误信息

## 已知问题及解决方案

### 1. MySQL GROUP BY 错误
**错误信息**:
```
Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'monitor_system.cs.consumption_date' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
```

**原因**: MySQL的`sql_mode=only_full_group_by`模式要求SELECT列表中的非聚合列必须出现在GROUP BY子句中。

**解决方案**: 已修复`selectConsumptionTrend`查询，使用不同的SQL语句处理不同的时间维度：
- 日度趋势: 直接使用`cs.consumption_date`进行GROUP BY
- 其他维度: 使用`MIN(cs.consumption_date)`作为代表日期

**测试验证**: 可以使用`sql/test_consumption_trend_sql.sql`脚本验证修复后的SQL语句。

### 2. 权限验证失败
**错误信息**:
```json
{
  "msg": "请求访问：/item/consumption-report/xxx，认证失败，无法访问系统资源",
  "code": 401
}
```

**解决方案**:
- 确认token有效性
- 检查权限配置是否正确
- 部分接口已临时移除权限验证用于测试
