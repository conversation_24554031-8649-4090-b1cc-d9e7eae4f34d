-- 测试修复后的消耗趋势查询SQL
-- 用于验证GROUP BY语句是否符合MySQL的only_full_group_by模式

-- 1. 测试日度趋势查询 (timeDimension = 1)
SELECT
    cs.consumption_date as date,
    DATE_FORMAT(cs.consumption_date, '%Y-%m-%d') as dateLabel,
    COALESCE(SUM(cs.total_quantity), 0) as consumption,
    COALESCE(SUM(cs.total_amount), 0) as amount,
    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
FROM item_consumption_summary cs
LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
    AND cs.consumption_date BETWEEN e.period_start_date AND e.period_end_date
WHERE cs.item_id = 'TEST_ITEM'
    AND cs.consumption_date BETWEEN '2024-01-01' AND '2024-12-31'
GROUP BY cs.consumption_date
ORDER BY cs.consumption_date
LIMIT 5;

-- 2. 测试周度趋势查询 (timeDimension = 2)
SELECT
    MIN(cs.consumption_date) as date,
    CONCAT(YEAR(MIN(cs.consumption_date)), '-W', WEEK(MIN(cs.consumption_date))) as dateLabel,
    COALESCE(SUM(cs.total_quantity), 0) as consumption,
    COALESCE(SUM(cs.total_amount), 0) as amount,
    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
FROM item_consumption_summary cs
LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
    AND cs.consumption_date BETWEEN e.period_start_date AND e.period_end_date
WHERE cs.item_id = 'TEST_ITEM'
    AND cs.consumption_date BETWEEN '2024-01-01' AND '2024-12-31'
GROUP BY YEAR(cs.consumption_date), WEEK(cs.consumption_date)
ORDER BY YEAR(cs.consumption_date), WEEK(cs.consumption_date)
LIMIT 5;

-- 3. 测试月度趋势查询 (timeDimension = 3)
SELECT
    MIN(cs.consumption_date) as date,
    DATE_FORMAT(MIN(cs.consumption_date), '%Y-%m') as dateLabel,
    COALESCE(SUM(cs.total_quantity), 0) as consumption,
    COALESCE(SUM(cs.total_amount), 0) as amount,
    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
FROM item_consumption_summary cs
LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
    AND cs.consumption_date BETWEEN e.period_start_date AND e.period_end_date
WHERE cs.item_id = 'TEST_ITEM'
    AND cs.consumption_date BETWEEN '2024-01-01' AND '2024-12-31'
GROUP BY YEAR(cs.consumption_date), MONTH(cs.consumption_date)
ORDER BY YEAR(cs.consumption_date), MONTH(cs.consumption_date)
LIMIT 5;

-- 4. 测试季度趋势查询 (timeDimension = 4)
SELECT
    MIN(cs.consumption_date) as date,
    CONCAT(YEAR(MIN(cs.consumption_date)), '-Q', QUARTER(MIN(cs.consumption_date))) as dateLabel,
    COALESCE(SUM(cs.total_quantity), 0) as consumption,
    COALESCE(SUM(cs.total_amount), 0) as amount,
    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
FROM item_consumption_summary cs
LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
    AND cs.consumption_date BETWEEN e.period_start_date AND e.period_end_date
WHERE cs.item_id = 'TEST_ITEM'
    AND cs.consumption_date BETWEEN '2024-01-01' AND '2024-12-31'
GROUP BY YEAR(cs.consumption_date), QUARTER(cs.consumption_date)
ORDER BY YEAR(cs.consumption_date), QUARTER(cs.consumption_date)
LIMIT 5;

-- 5. 测试年度趋势查询 (timeDimension = 5)
SELECT
    MIN(cs.consumption_date) as date,
    DATE_FORMAT(MIN(cs.consumption_date), '%Y') as dateLabel,
    COALESCE(SUM(cs.total_quantity), 0) as consumption,
    COALESCE(SUM(cs.total_amount), 0) as amount,
    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
FROM item_consumption_summary cs
LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
    AND cs.consumption_date BETWEEN e.period_start_date AND e.period_end_date
WHERE cs.item_id = 'TEST_ITEM'
    AND cs.consumption_date BETWEEN '2024-01-01' AND '2024-12-31'
GROUP BY YEAR(cs.consumption_date)
ORDER BY YEAR(cs.consumption_date)
LIMIT 5;

-- 检查当前MySQL的sql_mode设置
SELECT @@sql_mode;

-- 如果需要临时禁用only_full_group_by模式（仅用于测试）
-- SET sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''));
