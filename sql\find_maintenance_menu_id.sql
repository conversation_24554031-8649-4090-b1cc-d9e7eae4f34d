-- 查找设备维护菜单的menu_id
-- 请先执行此查询，确认设备维护菜单的实际ID

SELECT 
    menu_id,
    menu_name,
    parent_id,
    path,
    component,
    perms
FROM sys_menu 
WHERE menu_name LIKE '%维护%' OR menu_name LIKE '%maintenance%'
ORDER BY menu_id;

-- 如果没有找到设备维护菜单，可能需要先创建
-- 以下是创建设备维护主菜单的SQL（如果不存在的话）

-- INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- VALUES 
-- (
--     '设备维护',
--     0,                            -- 顶级菜单
--     4,                            -- 显示顺序
--     'maintenance',                -- 路由地址
--     '',                           -- 组件路径（目录类型为空）
--     '',                           -- 路由参数
--     1,                            -- 是否为外链（1是 0否）
--     0,                            -- 是否缓存（0缓存 1不缓存）
--     'M',                          -- 菜单类型（M目录 C菜单 F按钮）
--     '0',                          -- 显示状态（0显示 1隐藏）
--     '0',                          -- 菜单状态（0正常 1停用）
--     '',                           -- 权限标识（目录可为空）
--     'tool',                       -- 菜单图标
--     'admin',                      -- 创建者
--     NOW(),                        -- 创建时间
--     'admin',                      -- 更新者
--     NOW(),                        -- 更新时间
--     '设备维护管理目录'             -- 备注
-- );
